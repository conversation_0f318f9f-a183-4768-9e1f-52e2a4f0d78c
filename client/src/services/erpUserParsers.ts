import { 
  ERPVendor, 
  UserImportResult, 
  ParsedUser,
  ValidationError,
  ROLE_TRANSFORMATION_PRESETS 
} from "@/types/userImport";

/**
 * ERP User Parser Service
 * 
 * Handles parsing of user and role data from various ERP systems
 * into our canonical user format. Supports multiple file formats
 * and provides field mapping capabilities.
 */

export class ERPUserParserFactory {
  static parse(
    content: string, 
    vendor: ERPVendor, 
    format: string,
    fileName?: string
  ): UserImportResult {
    const warnings: string[] = [];
    const errors: string[] = [];
    let parsedUsers: ParsedUser[] = [];
    
    try {
      switch (vendor) {
        case "SAP":
          parsedUsers = SAPUserParser.parse(content, format);
          break;
        case "Oracle":
          parsedUsers = OracleUserParser.parse(content, format);
          break;
        case "Dynamics365":
          parsedUsers = Dynamics365UserParser.parse(content, format);
          break;
        case "NetSuite":
          parsedUsers = NetSuiteUserParser.parse(content, format);
          break;
        default:
          throw new Error(`Unsupported vendor: ${vendor}`);
      }
      
      return {
        vendor,
        format,
        users: parsedUsers,
        fieldMappings: this.generateDefaultFieldMappings(vendor),
        warnings,
        errors,
        metadata: {
          fileName,
          recordCount: parsedUsers.length,
          parsedAt: new Date().toISOString()
        }
      };
      
    } catch (error) {
      errors.push(error.message);
      return {
        vendor,
        format,
        users: [],
        fieldMappings: {},
        warnings,
        errors,
        metadata: { fileName, error: error.message }
      };
    }
  }
  
  private static generateDefaultFieldMappings(vendor: ERPVendor): Record<string, string> {
    const mappings: Record<ERPVendor, Record<string, string>> = {
      SAP: {
        'BNAME': 'erpUserId',
        'SMTP_ADDR': 'email',
        'NAME_FIRST': 'firstName',
        'NAME_LAST': 'lastName',
        'KOSTL': 'costCenter',
        'BUKRS': 'subsidiary',
        'USTYP': 'status',
        'AGR_NAME': 'erpRoleCodes'
      },
      Oracle: {
        'user_id': 'erpUserId',
        'email': 'email',
        'first_name': 'firstName',
        'last_name': 'lastName',
        'cost_center': 'costCenter',
        'legal_entity': 'subsidiary',
        'status': 'status',
        'roles': 'erpRoleCodes'
      },
      Dynamics365: {
        'systemuserid': 'erpUserId',
        'internalemailaddress': 'email',
        'firstname': 'firstName',
        'lastname': 'lastName',
        'businessunitid': 'costCenter',
        'organizationid': 'subsidiary',
        'isdisabled': 'status',
        'roles': 'erpRoleCodes'
      },
      NetSuite: {
        'id': 'erpUserId',
        'email': 'email',
        'firstname': 'firstName',
        'lastname': 'lastName',
        'department': 'costCenter',
        'subsidiary': 'subsidiary',
        'isinactive': 'status',
        'roles': 'erpRoleCodes'
      }
    };
    
    return mappings[vendor] || {};
  }
}

class SAPUserParser {
  static parse(content: string, format: string): ParsedUser[] {
    switch (format.toLowerCase()) {
      case 'json':
        return this.parseJSON(content);
      case 'csv':
        return this.parseCSV(content);
      case 'xml':
        return this.parseXML(content);
      default:
        throw new Error(`Unsupported SAP format: ${format}`);
    }
  }
  
  private static parseJSON(content: string): ParsedUser[] {
    const data = JSON.parse(content);
    
    // Handle SAP SUIM export format
    if (data.users) {
      return data.users.map((user: any) => ({
        rawData: user,
        email: user.SMTP_ADDR || user.email,
        firstName: user.NAME_FIRST || user.firstName,
        lastName: user.NAME_LAST || user.lastName,
        erpUserId: user.BNAME || user.userId,
        erpRoles: user.AGR_NAME ? [user.AGR_NAME] : (user.roles || []),
        costCenter: user.KOSTL || user.costCenter,
        subsidiary: user.BUKRS || user.subsidiary,
        status: this.mapSAPStatus(user.USTYP || user.status)
      }));
    }
    
    // Handle single user or array
    const users = Array.isArray(data) ? data : [data];
    return users.map(user => ({
      rawData: user,
      email: user.SMTP_ADDR || user.email,
      firstName: user.NAME_FIRST || user.firstName,
      lastName: user.NAME_LAST || user.lastName,
      erpUserId: user.BNAME || user.userId,
      erpRoles: user.AGR_NAME ? [user.AGR_NAME] : (user.roles || []),
      costCenter: user.KOSTL || user.costCenter,
      subsidiary: user.BUKRS || user.subsidiary,
      status: this.mapSAPStatus(user.USTYP || user.status)
    }));
  }
  
  private static parseCSV(content: string): ParsedUser[] {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];
    
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const users: ParsedUser[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      if (values.length >= headers.length) {
        const rawData = Object.fromEntries(headers.map((h, idx) => [h, values[idx]]));
        
        users.push({
          rawData,
          email: rawData['SMTP_ADDR'] || rawData['email'],
          firstName: rawData['NAME_FIRST'] || rawData['firstName'],
          lastName: rawData['NAME_LAST'] || rawData['lastName'],
          erpUserId: rawData['BNAME'] || rawData['userId'],
          erpRoles: rawData['AGR_NAME'] ? [rawData['AGR_NAME']] : [],
          costCenter: rawData['KOSTL'] || rawData['costCenter'],
          subsidiary: rawData['BUKRS'] || rawData['subsidiary'],
          status: this.mapSAPStatus(rawData['USTYP'] || rawData['status'])
        });
      }
    }
    
    return users;
  }
  
  private static parseXML(content: string): ParsedUser[] {
    // Basic XML parsing for SAP user exports
    const users: ParsedUser[] = [];
    const userMatches = content.match(/<user[^>]*>(.*?)<\/user>/gs);
    
    if (userMatches) {
      userMatches.forEach(match => {
        const emailMatch = match.match(/<email>([^<]+)<\/email>/);
        const firstNameMatch = match.match(/<firstName>([^<]+)<\/firstName>/);
        const lastNameMatch = match.match(/<lastName>([^<]+)<\/lastName>/);
        const userIdMatch = match.match(/<userId>([^<]+)<\/userId>/);
        
        if (emailMatch && firstNameMatch && lastNameMatch) {
          users.push({
            rawData: { xml: match },
            email: emailMatch[1],
            firstName: firstNameMatch[1],
            lastName: lastNameMatch[1],
            erpUserId: userIdMatch?.[1],
            erpRoles: [],
            status: "ACTIVE"
          });
        }
      });
    }
    
    return users;
  }
  
  private static mapSAPStatus(status: string): string {
    const statusMap: Record<string, string> = {
      '0': 'ACTIVE',
      '1': 'LOCKED',
      '2': 'PENDING',
      'ACTIVE': 'ACTIVE',
      'LOCKED': 'LOCKED',
      'INACTIVE': 'LOCKED'
    };
    
    return statusMap[status?.toUpperCase()] || 'ACTIVE';
  }
}

class OracleUserParser {
  static parse(content: string, format: string): ParsedUser[] {
    switch (format.toLowerCase()) {
      case 'json':
        return this.parseJSON(content);
      case 'xlsx':
      case 'csv':
        return this.parseCSV(content);
      default:
        throw new Error(`Unsupported Oracle format: ${format}`);
    }
  }
  
  private static parseJSON(content: string): ParsedUser[] {
    const data = JSON.parse(content);
    
    // Handle Oracle SCIM format
    if (data.Resources) {
      return data.Resources.map((user: any) => ({
        rawData: user,
        email: user.emails?.[0]?.value || user.email,
        firstName: user.name?.givenName || user.firstName,
        lastName: user.name?.familyName || user.lastName,
        erpUserId: user.id || user.userId,
        erpRoles: user.roles?.map((r: any) => r.value || r) || [],
        status: user.active ? 'ACTIVE' : 'LOCKED'
      }));
    }
    
    // Handle simple format
    const users = Array.isArray(data) ? data : [data];
    return users.map(user => ({
      rawData: user,
      email: user.email,
      firstName: user.first_name || user.firstName,
      lastName: user.last_name || user.lastName,
      erpUserId: user.user_id || user.id,
      erpRoles: user.roles || [],
      costCenter: user.cost_center,
      subsidiary: user.legal_entity,
      status: user.status === 'ACTIVE' ? 'ACTIVE' : 'LOCKED'
    }));
  }
  
  private static parseCSV(content: string): ParsedUser[] {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];
    
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const users: ParsedUser[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      if (values.length >= headers.length) {
        const rawData = Object.fromEntries(headers.map((h, idx) => [h, values[idx]]));
        
        users.push({
          rawData,
          email: rawData['email'] || rawData['Email'],
          firstName: rawData['first_name'] || rawData['First Name'],
          lastName: rawData['last_name'] || rawData['Last Name'],
          erpUserId: rawData['user_id'] || rawData['User ID'],
          erpRoles: rawData['roles'] ? rawData['roles'].split(';') : [],
          costCenter: rawData['cost_center'],
          subsidiary: rawData['legal_entity'],
          status: rawData['status'] === 'ACTIVE' ? 'ACTIVE' : 'LOCKED'
        });
      }
    }
    
    return users;
  }
}

class Dynamics365UserParser {
  static parse(content: string, format: string): ParsedUser[] {
    switch (format.toLowerCase()) {
      case 'json':
        return this.parseJSON(content);
      case 'xml':
        return this.parseXML(content);
      case 'csv':
        return this.parseCSV(content);
      default:
        throw new Error(`Unsupported Dynamics365 format: ${format}`);
    }
  }
  
  private static parseJSON(content: string): ParsedUser[] {
    const data = JSON.parse(content);
    
    // Handle Dynamics 365 OData format
    if (data.value) {
      return data.value.map((user: any) => ({
        rawData: user,
        email: user.internalemailaddress || user.email,
        firstName: user.firstname || user.firstName,
        lastName: user.lastname || user.lastName,
        erpUserId: user.systemuserid || user.id,
        erpRoles: user.roles || [],
        status: user.isdisabled ? 'LOCKED' : 'ACTIVE'
      }));
    }
    
    const users = Array.isArray(data) ? data : [data];
    return users.map(user => ({
      rawData: user,
      email: user.internalemailaddress || user.email,
      firstName: user.firstname || user.firstName,
      lastName: user.lastname || user.lastName,
      erpUserId: user.systemuserid || user.id,
      erpRoles: user.roles || [],
      status: user.isdisabled ? 'LOCKED' : 'ACTIVE'
    }));
  }
  
  private static parseXML(content: string): ParsedUser[] {
    // Basic XML parsing for Dynamics 365 exports
    const users: ParsedUser[] = [];
    const userMatches = content.match(/<systemuser[^>]*>(.*?)<\/systemuser>/gs);
    
    if (userMatches) {
      userMatches.forEach(match => {
        const emailMatch = match.match(/<internalemailaddress>([^<]+)<\/internalemailaddress>/);
        const firstNameMatch = match.match(/<firstname>([^<]+)<\/firstname>/);
        const lastNameMatch = match.match(/<lastname>([^<]+)<\/lastname>/);
        
        if (emailMatch && firstNameMatch && lastNameMatch) {
          users.push({
            rawData: { xml: match },
            email: emailMatch[1],
            firstName: firstNameMatch[1],
            lastName: lastNameMatch[1],
            erpRoles: [],
            status: "ACTIVE"
          });
        }
      });
    }
    
    return users;
  }
  
  private static parseCSV(content: string): ParsedUser[] {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];
    
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const users: ParsedUser[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      if (values.length >= headers.length) {
        const rawData = Object.fromEntries(headers.map((h, idx) => [h, values[idx]]));
        
        users.push({
          rawData,
          email: rawData['internalemailaddress'] || rawData['email'],
          firstName: rawData['firstname'] || rawData['firstName'],
          lastName: rawData['lastname'] || rawData['lastName'],
          erpUserId: rawData['systemuserid'] || rawData['id'],
          erpRoles: rawData['roles'] ? rawData['roles'].split(';') : [],
          status: rawData['isdisabled'] === 'true' ? 'LOCKED' : 'ACTIVE'
        });
      }
    }
    
    return users;
  }
}

class NetSuiteUserParser {
  static parse(content: string, format: string): ParsedUser[] {
    switch (format.toLowerCase()) {
      case 'json':
        return this.parseJSON(content);
      case 'xml':
        return this.parseXML(content);
      case 'csv':
        return this.parseCSV(content);
      default:
        throw new Error(`Unsupported NetSuite format: ${format}`);
    }
  }
  
  private static parseJSON(content: string): ParsedUser[] {
    const data = JSON.parse(content);
    
    // Handle NetSuite SuiteQL format
    if (data.items) {
      return data.items.map((user: any) => ({
        rawData: user,
        email: user.email,
        firstName: user.firstname || user.firstName,
        lastName: user.lastname || user.lastName,
        erpUserId: user.id,
        erpRoles: user.roles || [],
        department: user.department,
        subsidiary: user.subsidiary,
        status: user.isinactive ? 'LOCKED' : 'ACTIVE'
      }));
    }
    
    const users = Array.isArray(data) ? data : [data];
    return users.map(user => ({
      rawData: user,
      email: user.email,
      firstName: user.firstname || user.firstName,
      lastName: user.lastname || user.lastName,
      erpUserId: user.id,
      erpRoles: user.roles || [],
      department: user.department,
      subsidiary: user.subsidiary,
      status: user.isinactive ? 'LOCKED' : 'ACTIVE'
    }));
  }
  
  private static parseXML(content: string): ParsedUser[] {
    // Basic XML parsing for NetSuite exports
    const users: ParsedUser[] = [];
    const userMatches = content.match(/<employee[^>]*>(.*?)<\/employee>/gs);
    
    if (userMatches) {
      userMatches.forEach(match => {
        const emailMatch = match.match(/<email>([^<]+)<\/email>/);
        const firstNameMatch = match.match(/<firstname>([^<]+)<\/firstname>/);
        const lastNameMatch = match.match(/<lastname>([^<]+)<\/lastname>/);
        
        if (emailMatch && firstNameMatch && lastNameMatch) {
          users.push({
            rawData: { xml: match },
            email: emailMatch[1],
            firstName: firstNameMatch[1],
            lastName: lastNameMatch[1],
            erpRoles: [],
            status: "ACTIVE"
          });
        }
      });
    }
    
    return users;
  }
  
  private static parseCSV(content: string): ParsedUser[] {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];
    
    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const users: ParsedUser[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      if (values.length >= headers.length) {
        const rawData = Object.fromEntries(headers.map((h, idx) => [h, values[idx]]));
        
        users.push({
          rawData,
          email: rawData['email'],
          firstName: rawData['firstname'] || rawData['firstName'],
          lastName: rawData['lastname'] || rawData['lastName'],
          erpUserId: rawData['id'],
          erpRoles: rawData['roles'] ? rawData['roles'].split(';') : [],
          department: rawData['department'],
          subsidiary: rawData['subsidiary'],
          status: rawData['isinactive'] === 'true' ? 'LOCKED' : 'ACTIVE'
        });
      }
    }
    
    return users;
  }
}
