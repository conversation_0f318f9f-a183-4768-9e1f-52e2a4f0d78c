import { 
  UserSet, 
  User<PERSON>mportH<PERSON>ory, 
  User<PERSON>ieldMapping, 
  UserImportResult,
  ImportedUser,
  UserSetDiff 
} from "@/types/userImport";

// Helper function for API requests
async function apiRequest<T>(url: string, options?: RequestInit): Promise<T> {
  const response = await fetch(url, {
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    ...options,
  });
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({}));
    throw new Error(error.message || "An error occurred with the request");
  }
  
  return response.json();
}

export const userImportService = {
  // User Set endpoints
  getUserSets: async (): Promise<UserSet[]> => {
    return apiRequest<UserSet[]>("/api/admin/usersets");
  },
  
  getUserSet: async (id: string): Promise<UserSet> => {
    return apiRequest<UserSet>(`/api/admin/usersets/${id}`);
  },
  
  createUserSet: async (data: Omit<UserSet, "id" | "createdAt">): Promise<UserSet> => {
    return apiRequest<UserSet>("/api/admin/usersets", {
      method: "POST",
      body: JSON.stringify(data),
    });
  },
  
  activateUserSet: async (id: string): Promise<UserSet> => {
    return apiRequest<UserSet>(`/api/admin/usersets/${id}/activate`, {
      method: "POST",
    });
  },
  
  rollbackUserSet: async (id: string): Promise<{ success: boolean; message: string }> => {
    return apiRequest<{ success: boolean; message: string }>(`/api/admin/usersets/${id}/rollback`, {
      method: "POST",
    });
  },
  
  getUserSetDiff: async (fromId: string, toId: string): Promise<UserSetDiff> => {
    return apiRequest<UserSetDiff>(`/api/admin/usersets/${fromId}/diff/${toId}`);
  },
  
  // User Import endpoints
  importUsers: async (data: { vendor: string; content: string; filename?: string }): Promise<UserImportResult> => {
    return apiRequest<UserImportResult>("/api/admin/users/import", {
      method: "POST",
      body: JSON.stringify(data),
    });
  },
  
  pullUsersFromAPI: async (vendor: string, credentials: Record<string, string>): Promise<UserImportResult> => {
    return apiRequest<UserImportResult>(`/api/admin/users/import/${vendor}/pull`, {
      method: "POST",
      body: JSON.stringify(credentials),
    });
  },
  
  getImportHistory: async (): Promise<UserImportHistory[]> => {
    return apiRequest<UserImportHistory[]>("/api/admin/users/import/history");
  },
  
  getImportHistoryItem: async (id: string): Promise<UserImportHistory> => {
    return apiRequest<UserImportHistory>(`/api/admin/users/import/history/${id}`);
  },
  
  // Field Mapping endpoints
  getFieldMappings: async (vendor?: string): Promise<UserFieldMapping[]> => {
    const url = vendor ? `/api/admin/users/mappings?vendor=${vendor}` : "/api/admin/users/mappings";
    return apiRequest<UserFieldMapping[]>(url);
  },
  
  getFieldMapping: async (id: string): Promise<UserFieldMapping> => {
    return apiRequest<UserFieldMapping>(`/api/admin/users/mappings/${id}`);
  },
  
  createFieldMapping: async (data: Omit<UserFieldMapping, "id" | "createdAt" | "updatedAt">): Promise<UserFieldMapping> => {
    return apiRequest<UserFieldMapping>("/api/admin/users/mappings", {
      method: "POST",
      body: JSON.stringify(data),
    });
  },
  
  updateFieldMapping: async (id: string, data: Partial<UserFieldMapping>): Promise<UserFieldMapping> => {
    return apiRequest<UserFieldMapping>(`/api/admin/users/mappings/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },
  
  deleteFieldMapping: async (id: string): Promise<void> => {
    return apiRequest<void>(`/api/admin/users/mappings/${id}`, {
      method: "DELETE",
    });
  },
  
  // User Management endpoints
  getUsers: async (userSetId?: string): Promise<ImportedUser[]> => {
    const url = userSetId ? `/api/admin/users?userSetId=${userSetId}` : "/api/admin/users";
    return apiRequest<ImportedUser[]>(url);
  },
  
  getUser: async (id: string): Promise<ImportedUser> => {
    return apiRequest<ImportedUser>(`/api/admin/users/${id}`);
  },
  
  updateUser: async (id: string, data: Partial<ImportedUser>): Promise<ImportedUser> => {
    return apiRequest<ImportedUser>(`/api/admin/users/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },
  
  deleteUser: async (id: string): Promise<void> => {
    return apiRequest<void>(`/api/admin/users/${id}`, {
      method: "DELETE",
    });
  },
  
  // Permission Preview
  previewUserPermissions: async (userId: string): Promise<{
    visibleDocuments: { invoices: number; payments: number };
    approvalAuthority: { tier: string; maxAmount: number; workflows: string[] };
    accessScopes: { customers: string[]; legalEntities: string[]; costCenters: string[] };
  }> => {
    return apiRequest(`/api/admin/users/${userId}/permissions/preview`);
  },
  
  // Validation
  validateUserSet: async (userSetId: string): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> => {
    return apiRequest(`/api/admin/usersets/${userSetId}/validate`);
  },
  
  validateUser: async (user: Partial<ImportedUser>): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> => {
    return apiRequest("/api/admin/users/validate", {
      method: "POST",
      body: JSON.stringify(user),
    });
  },
  
  // Sample Files
  downloadSampleFile: (vendor: string): void => {
    window.open(`/api/admin/users/sample/${vendor}`, '_blank');
  },
  
  // Bulk Operations
  bulkUpdateUsers: async (userIds: string[], updates: Partial<ImportedUser>): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> => {
    return apiRequest("/api/admin/users/bulk-update", {
      method: "POST",
      body: JSON.stringify({ userIds, updates }),
    });
  },
  
  bulkDeleteUsers: async (userIds: string[]): Promise<{
    success: number;
    failed: number;
    errors: string[];
  }> => {
    return apiRequest("/api/admin/users/bulk-delete", {
      method: "POST",
      body: JSON.stringify({ userIds }),
    });
  },
  
  // Export
  exportUsers: async (userSetId: string, format: "csv" | "xlsx" | "json"): Promise<Blob> => {
    const response = await fetch(`/api/admin/usersets/${userSetId}/export?format=${format}`, {
      credentials: "include",
    });
    
    if (!response.ok) {
      throw new Error("Failed to export users");
    }
    
    return response.blob();
  },
  
  // Sync with RBAC system
  syncWithRBAC: async (userSetId: string): Promise<{
    success: boolean;
    syncedUsers: number;
    errors: string[];
  }> => {
    return apiRequest(`/api/admin/usersets/${userSetId}/sync-rbac`, {
      method: "POST",
    });
  },
  
  // Workflow invalidation
  invalidateWorkflows: async (userSetId: string): Promise<{
    success: boolean;
    invalidatedWorkflows: number;
    errors: string[];
  }> => {
    return apiRequest(`/api/admin/usersets/${userSetId}/invalidate-workflows`, {
      method: "POST",
    });
  },
};
