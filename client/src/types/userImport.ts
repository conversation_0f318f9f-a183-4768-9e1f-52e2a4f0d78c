// User Import System Types
// Defines the complete type system for the Import-Driven User & Role module

export type ERPVendor = "SAP" | "Oracle" | "Dynamics365" | "NetSuite";

export type ProofPayRole = "AR" | "AP" | "Approver_T1" | "Approver_T2" | "AccountOwner";

export type UserStatus = "ACTIVE" | "LOCKED" | "PENDING";

export type ImportType = "FILE" | "API_PULL" | "MANUAL";

export type UserSetStatus = "DRAFT" | "ACTIVE" | "ARCHIVED";

export type ImportStatus = "PROCESSING" | "COMPLETED" | "FAILED";

// Canonical user schema as defined in the requirements
export interface ImportedUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  erpUserId?: string;
  erpRoleCodes: string[];
  proofpayRoles: ProofPayRole[];
  customerFilters: string[];
  legalEntityScope?: string[];
  costCenter?: string;
  subsidiary?: string;
  status: UserStatus;
  attributes?: Record<string, any>;
}

// User set for versioning and rollback
export interface UserSet {
  id: string;
  version: string;
  name: string;
  description?: string;
  vendor?: ERPVendor;
  status: UserSetStatus;
  userCount: number;
  changeLog?: string;
  createdAt: string;
  createdBy: string;
  activatedAt?: string;
  users?: ImportedUser[];
}

// Import operation metadata
export interface UserImportHistory {
  id: string;
  userSetId: string;
  vendor?: ERPVendor;
  importType: ImportType;
  fileName?: string;
  fileSize?: number;
  recordsProcessed: number;
  recordsSuccessful: number;
  recordsSkipped: number;
  recordsErrored: number;
  validationErrors?: ValidationError[];
  mappingId?: string;
  processingTimeMs?: number;
  status: ImportStatus;
  errorMessage?: string;
  createdAt: string;
  completedAt?: string;
  createdBy: string;
}

// Field mapping for ERP user imports
export interface UserFieldMapping {
  id: string;
  name: string;
  vendor: ERPVendor;
  mappings: Record<string, string>; // vendor_field -> canonical_field
  roleTransformations?: Record<string, ProofPayRole[]>; // erp_role -> proofpay_roles
  defaultAttributes?: Record<string, any>;
  validationRules?: ValidationRule[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

// Validation error structure
export interface ValidationError {
  field: string;
  value: any;
  message: string;
  row?: number;
  severity: "error" | "warning";
}

// Validation rule structure
export interface ValidationRule {
  field: string;
  type: "required" | "email" | "domain" | "unique" | "enum" | "regex";
  value?: any;
  message: string;
}

// Import result from ERP parsers
export interface UserImportResult {
  vendor: ERPVendor;
  format: string;
  users: ParsedUser[];
  fieldMappings: Record<string, string>;
  warnings: string[];
  errors: string[];
  metadata: Record<string, any>;
}

// Parsed user from ERP systems (before mapping)
export interface ParsedUser {
  rawData: Record<string, any>;
  email?: string;
  firstName?: string;
  lastName?: string;
  erpUserId?: string;
  erpRoles?: string[];
  department?: string;
  costCenter?: string;
  subsidiary?: string;
  status?: string;
}

// Diff between user sets
export interface UserSetDiff {
  added: ImportedUser[];
  removed: ImportedUser[];
  modified: UserModification[];
  unchanged: ImportedUser[];
  summary: {
    totalChanges: number;
    addedCount: number;
    removedCount: number;
    modifiedCount: number;
  };
}

// User modification details
export interface UserModification {
  user: ImportedUser;
  changes: FieldChange[];
}

// Field change details
export interface FieldChange {
  field: string;
  oldValue: any;
  newValue: any;
  type: "role_added" | "role_removed" | "filter_changed" | "status_changed" | "attribute_changed";
}

// Permission preview for simulation
export interface UserPermissionPreview {
  user: ImportedUser;
  visibleDocuments: {
    invoices: number;
    payments: number;
  };
  approvalAuthority: {
    tier: "T1" | "T2" | "None";
    maxAmount?: number;
    workflows: string[];
  };
  accessScopes: {
    customers: string[];
    legalEntities: string[];
    costCenters: string[];
  };
}

// API connector configuration
export interface UserImportConnector {
  id: string;
  name: string;
  vendor: ERPVendor;
  type: "API" | "FILE";
  config: {
    baseUrl?: string;
    apiKey?: string;
    username?: string;
    password?: string;
    endpoint?: string;
    pollInterval?: number;
    filePattern?: string;
    directory?: string;
  };
  lastSync?: string;
  status: "ACTIVE" | "ERROR" | "DISABLED";
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

// Canonical field definitions for mapping wizard
export const CANONICAL_USER_FIELDS = {
  "email": "User email address (required)",
  "firstName": "User first name (required)",
  "lastName": "User last name (required)",
  "erpUserId": "Original ERP user ID",
  "erpRoleCodes": "Array of ERP role codes",
  "proofpayRoles": "Array of ProofPay roles (AR, AP, Approver_T1, etc.)",
  "customerFilters": "Array of customer account names for filtering",
  "legalEntityScope": "Array of legal entity codes",
  "costCenter": "Cost center code",
  "subsidiary": "Subsidiary code",
  "status": "User status (ACTIVE, LOCKED, PENDING)",
  "department": "Department name",
  "manager": "Manager email or ID",
  "location": "Office location",
  "title": "Job title",
} as const;

// Role transformation presets
export const ROLE_TRANSFORMATION_PRESETS: Record<ERPVendor, Record<string, ProofPayRole[]>> = {
  SAP: {
    "F_BKPF_BUK": ["AP"],
    "F_BKPF_GST": ["AR"],
    "F_BKPF_APP": ["Approver_T1"],
    "F_BKPF_MGR": ["Approver_T2"],
    "F_BKPF_OWN": ["AccountOwner"],
  },
  Oracle: {
    "AP_INVOICE_ENTRY": ["AP"],
    "AR_INVOICE_ENTRY": ["AR"],
    "AP_APPROVAL_LEVEL1": ["Approver_T1"],
    "AP_APPROVAL_LEVEL2": ["Approver_T2"],
    "ACCOUNT_OWNER": ["AccountOwner"],
  },
  Dynamics365: {
    "Accounts payable clerk": ["AP"],
    "Accounts receivable clerk": ["AR"],
    "Purchasing manager": ["Approver_T1"],
    "Finance manager": ["Approver_T2"],
    "Account owner": ["AccountOwner"],
  },
  NetSuite: {
    "AP Clerk": ["AP"],
    "AR Clerk": ["AR"],
    "Approver": ["Approver_T1"],
    "Senior Approver": ["Approver_T2"],
    "Administrator": ["AccountOwner"],
  },
};

// Validation constraints
export const VALIDATION_CONSTRAINTS = {
  EMAIL_DOMAIN_REQUIRED: true,
  UNIQUE_ROLE_CONSTRAINT: true, // User cannot be both Approver_T1 and Approver_T2
  ACCOUNT_OWNER_REQUIRED: true, // At least one active AccountOwner required
  MAX_USERS_PER_IMPORT: 10000,
  SUPPORTED_FILE_FORMATS: [".csv", ".xlsx", ".json", ".xml", ".zip"],
  MAX_FILE_SIZE_MB: 50,
} as const;
