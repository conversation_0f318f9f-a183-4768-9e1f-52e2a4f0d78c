import { useState, useEffect } from "react";
import { formatSafeDate } from "@/lib/dateUtils";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useQueryClient } from "@tanstack/react-query";
import { X } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useInvoices } from "@/hooks/useInvoices";
import ProcessingOverlay from "./ProcessingOverlay";
import { saveAs } from "file-saver";

/**
 * DetailPanel Component
 *
 * Displays detailed information and action buttons for payments, invoices, and received payments.
 * Provides functionality for approving/revoking payments, sending payments, generating remittances,
 * and linking received payments to invoices.
 *
 * The panel can be displayed either as a slide-out panel next to a column or as a modal dialog.
 */

interface DetailPanelProps {
  isOpen: boolean;                 // Whether the panel is currently open
  onClose: () => void;             // Handler to close the panel
  item: any;                       // The payment/invoice/received payment item to display
  itemType: "payment" | "invoice" | "receivedPayment"; // Type of item being displayed
  onRefresh: () => void;           // Function to refresh data after actions
  inlineMode?: boolean;            // Whether to display as inline panel (true) or modal (false)
  columnId?: string;               // Which column the panel is opened from (for context-specific actions)
  paymentOperations?: any;         // Payment-related operations from usePayments hook
}

const DetailPanel = ({
  isOpen,
  onClose,
  item,
  itemType,
  onRefresh,
  inlineMode = false,
  columnId,
  paymentOperations
}: DetailPanelProps) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [remittanceFormat, setRemittanceFormat] = useState<string>("MT940");

  // State for blockchain processing overlay
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingTitle, setProcessingTitle] = useState("");
  const [processingMessage, setProcessingMessage] = useState("");

  // State for payment sending overlay (transparent overlay over detail panel)
  const [isSendingPayment, setIsSendingPayment] = useState(false);

  // State for invoice linking modal
  const [isLinkInvoiceModalOpen, setIsLinkInvoiceModalOpen] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<number | null>(null);

  // State to track signature verification status
  const [verificationStatus, setVerificationStatus] = useState<{ isVerified: boolean; timestamp: string | null }>({
    isVerified: false,
    timestamp: null
  });

  // Get open invoices list for linking
  const { openInvoices = [] } = useInvoices();

  // Set default remittance format if not selected yet
  useEffect(() => {
    if (!remittanceFormat) {
      setRemittanceFormat("MT940");
    }
  }, [remittanceFormat]);

  /**
   * Handle payment approval action
   * Changes payment status from "Not Approved" to "Approved"
   * Only available for payments in the approval column with "Not Approved" status
   */
  const handleApprovePayment = async () => {
    if (!item || !paymentOperations?.approvePaymentMutation) return;

    try {
      setIsLoading(true);

      // Show immediate feedback with a toast
      toast({
        title: "Processing Approval",
        description: "Approving payment...",
      });

      // Use the mutation from the hook - this handles optimistic updates properly
      await paymentOperations.approvePaymentMutation.mutateAsync(item.id);

      // Show success message
      toast({
        title: "Payment Approved",
        description: "The payment has been approved and is ready to be sent.",
      });

      // Refresh data to get any additional server-side changes
      onRefresh();

      // Auto-close panel
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to approve payment: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle revoking a payment approval
   * Changes payment status from "Approved" back to "Not Approved"
   * Only available for payments in the approval column with "Approved" status
   * This allows recalling an approval before the payment is sent
   */
  const handleRevokeApproval = async () => {
    if (!item || !paymentOperations?.revokePaymentMutation) return;

    try {
      setIsLoading(true);

      // Show immediate feedback with a toast
      toast({
        title: "Revoking Approval",
        description: "Reverting payment to not approved status...",
      });

      // Use the mutation from the hook - this handles optimistic updates properly
      await paymentOperations.revokePaymentMutation.mutateAsync(item.id);

      // Show success message
      toast({
        title: "Approval Revoked",
        description: "The payment approval has been revoked.",
      });

      // Refresh data to get any additional server-side changes
      onRefresh();

      // Auto-close panel
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to revoke approval: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle sending a payment to the blockchain network
   * Changes payment status from "Approved" to "Paid"
   * Only available for payments in the payment column with "Approved" status
   * Shows a blockchain transaction animation overlay during processing
   * Downloads a tx.json file containing transaction details
   */
  const handleSendPayment = async () => {
    if (!item || !paymentOperations) return;

    // Verify that the payment has been approved and signed
    if (!item.signature || !item.message) {
      toast({
        title: "Error",
        description: "Payment must be approved and signed before sending.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      setIsSendingPayment(true);

      // Use the mutation from the hook - this handles optimistic updates properly
      // The mutation will update the React Query cache with the correct data
      await paymentOperations.sendPaymentMutation.mutateAsync(item.id);

      // Wait 4.8 seconds (slightly less than the server simulation of 5s)
      // This allows the animations to run before the final confirmation
      setTimeout(() => {
        // Force a final refresh to ensure we have updated data
        onRefresh();
        setIsLoading(false);
        setIsSendingPayment(false);

        // Auto-close panel after animations complete
        setTimeout(() => {
          onClose();
        }, 600); // Match with animation duration
      }, 4800);
    } catch (error) {
      setIsSendingPayment(false);
      setIsLoading(false);

      toast({
        title: "Error",
        description: `Failed to send payment: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
    }
  };

  const handleGenerateRemittance = async () => {
    if (!item || !paymentOperations?.generateRemittanceMutation) return;

    try {
      setIsLoading(true);

      // Show immediate toast notification
      toast({
        title: "Generating Remittance",
        description: `Creating ${remittanceFormat} remittance file...`,
      });

      // Generate the file content locally
      let fileContent = '';

      if (remittanceFormat === 'MT940') {
        fileContent = generateMT940Content(item);
      } else if (remittanceFormat === 'BAI2') {
        fileContent = generateBAI2Content(item);
      } else {
        fileContent = generateISO20022Content(item);
      }

      // Create and download the file
      const blob = new Blob([fileContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `remittance_${item.id}_${remittanceFormat}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Use the mutation from the hook - this handles optimistic updates properly
      await paymentOperations.generateRemittanceMutation.mutateAsync({
        id: item.id,
        format: remittanceFormat,
      });

      // Show success message
      toast({
        title: "Remittance Generated",
        description: `${remittanceFormat} remittance file has been generated and downloaded.`,
      });

      // Refresh to get actual remittance ID from server
      onRefresh();

      // Auto-close panel
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to generate remittance: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to generate MT940 format content
  const generateMT940Content = (payment: any) => {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const amount = payment.amount.toFixed(2).replace('.', ',');

    return `:20:${payment.reference}
:25:${payment.recipient_account || 'ACCOUNT'}
:28C:00001/001
:60F:C${date}USD${amount}
:61:${date}CR${amount}NMSCNONREF
:86:Payment to ${payment.recipient}
:62F:C${date}USD${amount}
-`;
  };

  // Helper function to generate BAI2 format content
  const generateBAI2Content = (payment: any) => {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const amount = payment.amount.toFixed(2);

    return `01,${payment.sender},${payment.recipient},${date},${date},USD,1
02,${payment.recipient_account || 'ACCOUNT'},1,030,${amount},,,${payment.reference}
03,${payment.reference},${amount},${payment.recipient},Payment
88,${amount},1,1
99,${amount},1,1`;
  };

  // Helper function to generate ISO20022 format content
  const generateISO20022Content = (payment: any) => {
    const date = new Date().toISOString();
    const amount = payment.amount.toFixed(2);

    return `<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03">
  <CstmrCdtTrfInitn>
    <GrpHdr>
      <MsgId>${payment.reference}</MsgId>
      <CreDtTm>${date}</CreDtTm>
      <NbOfTxs>1</NbOfTxs>
      <CtrlSum>${amount}</CtrlSum>
      <InitgPty>
        <Nm>${payment.sender}</Nm>
      </InitgPty>
    </GrpHdr>
    <PmtInf>
      <PmtInfId>${payment.reference}</PmtInfId>
      <PmtMtd>TRF</PmtMtd>
      <ReqdExctnDt>${date.slice(0, 10)}</ReqdExctnDt>
      <Dbtr>
        <Nm>${payment.sender}</Nm>
      </Dbtr>
      <CdtTrfTxInf>
        <PmtId>
          <EndToEndId>${payment.reference}</EndToEndId>
        </PmtId>
        <Amt>
          <InstdAmt Ccy="USD">${amount}</InstdAmt>
        </Amt>
        <Cdtr>
          <Nm>${payment.recipient}</Nm>
        </Cdtr>
        <CdtrAcct>
          <Id>
            <Othr>
              <Id>${payment.recipient_account || 'ACCOUNT'}</Id>
            </Othr>
          </Id>
        </CdtrAcct>
      </CdtTrfTxInf>
    </PmtInf>
  </CstmrCdtTrfInitn>
</Document>`;
  };

  const handleDownloadRemittance = () => {
    if (!item.remittance_id) return;

    // Create an anchor element and trigger the download
    const downloadUrl = `/api/remittances/${item.remittance_id}/download`;
    const downloadLink = document.createElement('a');
    downloadLink.href = downloadUrl;
    downloadLink.download = `remittance_${item.remittance_id}_${remittanceFormat}.txt`;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    toast({
      title: "Downloading Remittance",
      description: "Your remittance file is being downloaded.",
    });
  };

  // Handle linking received payment to invoice
  const handleLinkToInvoice = (e: React.MouseEvent) => {
    // Prevent event propagation to parent elements
    e.preventDefault();
    e.stopPropagation();

    // Reset the selected invoice ID before opening the modal
    setSelectedInvoiceId(null);
    setIsLinkInvoiceModalOpen(true);
    console.log("Opening link to invoice modal");
  };

  const handleLinkToInvoiceSubmit = async () => {
    if (!selectedInvoiceId) {
      toast({
        title: "Error",
        description: "Please select an invoice to link.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      // Show blockchain processing overlay for better UX
      setIsProcessing(true);
      setProcessingTitle("Processing Payment Link");
      setProcessingMessage("Verifying transaction on blockchain network...");

      await apiRequest("POST", `/api/received-payments/${item.id}/link-invoice`, {
        invoiceId: selectedInvoiceId,
      });

      // Manually force-refresh both datasets to ensure they're in sync
      Promise.all([
        fetch('/api/received-payments'),
        fetch('/api/invoices')
      ]).then(responses =>
        Promise.all(responses.map(r => r.json()))
      ).then(([payments, invoices]) => {
        // Update query cache directly for instant UI update
        queryClient.setQueryData(['/api/received-payments'], payments);
        queryClient.setQueryData(['/api/invoices'], invoices);
        console.log('Payment and invoice data refreshed after linking');
      }).catch(error => {
        console.error('Error refreshing data after linking:', error);
      });

      toast({
        title: "Success",
        description: "Payment has been linked to the selected invoice.",
      });

      setIsLinkInvoiceModalOpen(false);
      onRefresh();

      // Hide processing overlay after delay
      setTimeout(() => {
        setIsProcessing(false);
      }, 1500);
    } catch (error) {
      setIsProcessing(false);
      toast({
        title: "Error",
        description: `Failed to link invoice: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle generating invoice remittance
  const handleGenerateInvoiceRemittance = async () => {
    try {
      setIsLoading(true);

      // First, generate the remittance file content locally
      let fileContent = '';

      if (remittanceFormat === 'MT940') {
        fileContent = generateInvoiceMT940Content(item);
      } else if (remittanceFormat === 'BAI2') {
        fileContent = generateInvoiceBAI2Content(item);
      } else {
        fileContent = generateInvoiceISO20022Content(item);
      }

      // Create and download the file
      const blob = new Blob([fileContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `invoice_remittance_${item.id}_${remittanceFormat}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      // Then make the API call to register the remittance
      await apiRequest("POST", `/api/invoices/${item.id}/generate-remittance`, {
        format: remittanceFormat,
      });

      toast({
        title: "Remittance Generated",
        description: `${remittanceFormat} remittance file has been generated and downloaded.`,
      });

      onRefresh();

      // Auto-close panel
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to generate remittance: ${error instanceof Error ? error.message : "Unknown error"}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to generate MT940 format content for invoice
  const generateInvoiceMT940Content = (invoice: any) => {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const amount = invoice.amount.toFixed(2).replace('.', ',');

    return `:20:${invoice.reference}
:25:CUSTOMER_ACCOUNT
:28C:00001/001
:60F:C${date}USD${amount}
:61:${date}CR${amount}NMSCNONREF
:86:Invoice to ${invoice.customer}
:62F:C${date}USD${amount}
-`;
  };

  // Helper function to generate BAI2 format content for invoice
  const generateInvoiceBAI2Content = (invoice: any) => {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const amount = invoice.amount.toFixed(2);

    return `01,${invoice.customer},Sender,${date},${date},USD,1
02,CUSTOMER_ACCOUNT,1,030,${amount},,,${invoice.reference}
03,${invoice.reference},${amount},${invoice.customer},Invoice Payment
88,${amount},1,1
99,${amount},1,1`;
  };

  // Helper function to generate ISO20022 format content for invoice
  const generateInvoiceISO20022Content = (invoice: any) => {
    const date = new Date().toISOString();
    const amount = invoice.amount.toFixed(2);

    return `<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03">
  <CstmrCdtTrfInitn>
    <GrpHdr>
      <MsgId>${invoice.reference}</MsgId>
      <CreDtTm>${date}</CreDtTm>
      <NbOfTxs>1</NbOfTxs>
      <CtrlSum>${amount}</CtrlSum>
      <InitgPty>
        <Nm>${invoice.customer}</Nm>
      </InitgPty>
    </GrpHdr>
    <PmtInf>
      <PmtInfId>${invoice.reference}</PmtInfId>
      <PmtMtd>TRF</PmtMtd>
      <ReqdExctnDt>${date.slice(0, 10)}</ReqdExctnDt>
      <Dbtr>
        <Nm>${invoice.customer}</Nm>
      </Dbtr>
      <CdtTrfTxInf>
        <PmtId>
          <EndToEndId>${invoice.reference}</EndToEndId>
        </PmtId>
        <Amt>
          <InstdAmt Ccy="USD">${amount}</InstdAmt>
        </Amt>
        <Cdtr>
          <Nm>Your Company</Nm>
        </Cdtr>
        <CdtrAcct>
          <Id>
            <Othr>
              <Id>YOUR_ACCOUNT</Id>
            </Othr>
          </Id>
        </CdtrAcct>
      </CdtTrfTxInf>
    </PmtInf>
  </CstmrCdtTrfInitn>
</Document>`;
  };

  // Handle downloading invoice remittance
  const handleDownloadInvoiceRemittance = () => {
    if (!item.remittance_id) return;

    // Create an anchor element and trigger the download
    const downloadUrl = `/api/invoices/${item.id}/download-remittance`;
    const downloadLink = document.createElement('a');
    downloadLink.href = downloadUrl;
    downloadLink.download = `invoice_remittance_${item.id}_${remittanceFormat}.txt`;
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    toast({
      title: "Downloading Remittance",
      description: "Your invoice remittance file is being downloaded.",
    });
  };

  // Use our shared formatSafeDate utility function for date formatting

  // Format currency amount
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  // Get status badge class
  const getStatusBadgeClass = () => {
    let badgeClass = "text-sm font-medium px-3 py-1.5 rounded-full";

    switch (item.status) {
      case "Approved":
        return `${badgeClass} bg-emerald-50 text-emerald-700`;
      case "Not Approved":
        return `${badgeClass} bg-amber-50 text-amber-700`;
      case "Sent":
        return `${badgeClass} bg-blue-50 text-blue-700`;
      case "Paid":
        return `${badgeClass} bg-blue-50 text-blue-700`;
      case "Remitted":
        return `${badgeClass} bg-gray-100 text-gray-700`;
      case "Open":
        return `${badgeClass} bg-amber-50 text-amber-700`;
      case "Overdue":
        return `${badgeClass} bg-red-50 text-red-700`;
      case "Reconciled":
        return `${badgeClass} bg-gray-100 text-gray-700`;
      case "Unlinked":
        return `${badgeClass} bg-amber-50 text-amber-700`;
      case "Linked":
        return `${badgeClass} bg-blue-50 text-blue-700`;
      default:
        return `${badgeClass} bg-gray-100 text-gray-700`;
    }
  };

  // The panel content (shared between both modes)
  const contentSection = (
    <>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-medium tracking-tight text-gray-900">
          {itemType === "payment"
            ? "Payment Details"
            : itemType === "invoice"
            ? "Invoice Details"
            : "Received Payment Details"}
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 transition-colors rounded-full hover:bg-gray-50 p-1"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <div className="space-y-7">
        {/* Status Badge */}
        <div className="flex justify-center">
          <span className={getStatusBadgeClass()}>
            {(itemType === "receivedPayment" || itemType === "invoice" || itemType === "payment") && item.status === "Remitted"
              ? "Reconciled"
              : item.status}
          </span>
        </div>

        {/* Payment or Invoice Information */}
        <div className="space-y-5">
          <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
            {itemType === "payment"
              ? "Payment Information"
              : itemType === "invoice"
              ? "Invoice Information"
              : "Payment Information"}
          </h4>
          <div className="grid grid-cols-2 gap-5 bg-gray-50 p-4 rounded-lg border border-gray-100">
            <div>
              <p className="text-xs text-gray-500">Reference</p>
              <p className="font-medium">{item.reference}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500">Amount</p>
              <p className="font-medium">{formatCurrency(item.amount)}</p>
            </div>
            {itemType === "payment" && (
              <>
                <div>
                  <p className="text-xs text-gray-500">Due Date</p>
                  <p className="font-medium">
                    {item.due_date ? formatSafeDate(item.due_date, "MMM dd, yyyy") : 'No due date'}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">
                    {item.sent_at ? "Sent Date" : "Created Date"}
                  </p>
                  <p className="font-medium">
                    {item.sent_at
                      ? formatSafeDate(item.sent_at, "MMM dd, yyyy")
                      : formatSafeDate(item.created_at, "MMM dd, yyyy")}
                  </p>
                </div>

              </>
            )}
            {itemType === "invoice" && (
              <>
                <div>
                  <p className="text-xs text-gray-500">Due Date</p>
                  <p className="font-medium">{formatSafeDate(item.due_date, "MMM dd, yyyy")}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Created Date</p>
                  <p className="font-medium">
                    {formatSafeDate(item.created_at, "MMM dd, yyyy")}
                  </p>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Sender/Recipient Information */}
        <div className="space-y-5">
          <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
            Parties
          </h4>
          <div className="grid grid-cols-1 gap-4 bg-gray-50 p-4 rounded-lg border border-gray-100">
            {itemType === "invoice" ? (
              <div>
                <p className="text-xs text-gray-500">Customer</p>
                <p className="font-medium">{item.customer}</p>
              </div>
            ) : (
              <>
                <div>
                  <p className="text-xs text-gray-500">Sender</p>
                  <p className="font-medium">{item.sender || "N/A"}</p>
                </div>
                {itemType === "payment" && (
                  <div>
                    <p className="text-xs text-gray-500">Recipient</p>
                    <p className="font-medium">{item.recipient}</p>
                    {item.recipient_account ? (
                      <p className="text-sm font-mono bg-gray-100 p-1 px-2 rounded mt-2 border border-gray-200">
                        {item.recipient_account}
                      </p>
                    ) : item.recipient_address ? (
                      <p className="text-sm font-mono bg-gray-100 p-1 px-2 rounded mt-2 border border-gray-200">
                        {item.recipient_address}
                      </p>
                    ) : (
                      <p className="text-xs text-gray-500 italic mt-1">No account information</p>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {itemType === "invoice" && (
          <div className="space-y-5">
            <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
              Description
            </h4>
            <p className="text-sm text-gray-700 bg-gray-50 p-4 rounded-lg border border-gray-100">{item.description}</p>
          </div>
        )}

        {/* Transaction Data - only show for approved payments with signatures and not paid */}
        {itemType === "payment" && item.approved && item.signature && item.status !== "Paid" && (
          <div className="space-y-5">
            <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 mr-1 text-primary"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              Transaction Data
            </h4>
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
              <div className="mb-4">
                <p className="text-xs text-gray-500 mb-1">Signature Hash</p>
                <p className="text-xs font-mono bg-gray-100 p-2 rounded overflow-x-auto whitespace-nowrap">
                  {item.signature || "No signature available"}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500 mb-1">Message Hash</p>
                <p className="text-xs font-mono bg-gray-100 p-2 rounded overflow-x-auto whitespace-nowrap">
                  {item.message || "No message hash available"}
                </p>
              </div>
            </div>
          </div>
        )}

        <Separator />

        {/* Action Buttons */}
        <div className="mt-8 space-y-3">
          {/* Payment Actions */}
          {itemType === "payment" && !item.approved && (
            <Button
              className="w-full bg-emerald-500 hover:bg-emerald-600 shadow-sm font-medium tracking-tight"
              onClick={handleApprovePayment}
              disabled={isLoading}
            >
              Approve Payment
            </Button>
          )}

          {itemType === "payment" && item.approved && !item.sent_at && (
            <>
              {/* Only show Send Payment button in the approved column OR in the not-approved column
                  if we're not showing the Revoke Approval button there */}
              {(columnId === "approved" || (columnId === "not-approved" && !item.status.includes("Approved"))) && (
                <Button
                  className="w-full bg-primary hover:bg-primary/90 shadow-sm font-medium tracking-tight"
                  onClick={handleSendPayment}
                  disabled={isLoading}
                >
                  Send Payment
                </Button>
              )}

              {/* Only show the Revoke button for approved items in the not-approved column */}
              {columnId === "not-approved" && item.status === "Approved" && (
                <Button
                  variant="outline"
                  className="w-full text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-gray-900 font-medium tracking-tight"
                  onClick={handleRevokeApproval}
                  disabled={isLoading}
                >
                  Revoke Approval
                </Button>
              )}
            </>
          )}

          {itemType === "payment" && (item.status === "Paid" || item.status === "Sent") && !item.remittance_generated && (
            <Button
              className="w-full bg-primary hover:bg-primary/90 shadow-sm font-medium tracking-tight"
              onClick={handleGenerateRemittance}
              disabled={isLoading}
            >
              Generate Reconciliation
            </Button>
          )}

          {itemType === "payment" && item.remittance_generated && item.status !== "Paid" && item.status !== "Sent" && (
            <>
              <Button
                variant="outline"
                className="w-full text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-gray-900 font-medium tracking-tight"
                onClick={handleDownloadRemittance}
                disabled={isLoading}
              >
                Download Reconciliation
              </Button>
              <Button
                variant="outline"
                className="w-full border-blue-300 text-blue-600 hover:bg-blue-50 mt-2 font-medium tracking-tight"
                onClick={handleGenerateRemittance}
                disabled={isLoading || !remittanceFormat}
              >
                Regenerate Reconciliation
              </Button>
            </>
          )}

          {/* Received Payment Actions */}
          {itemType === "receivedPayment" && !item.invoice_id && (
            <Button
              className="w-full bg-primary hover:bg-primary/90 shadow-sm font-medium tracking-tight"
              onClick={(e) => handleLinkToInvoice(e)}
              disabled={isLoading}
            >
              Link to Invoice
            </Button>
          )}

          {/* Invoice Actions */}
          {itemType === "invoice" && item.payment_id && !item.remittance_generated && (
            <Button
              className="w-full bg-primary hover:bg-primary/90 shadow-sm font-medium tracking-tight"
              onClick={handleGenerateInvoiceRemittance}
              disabled={isLoading}
            >
              Generate Reconciliation
            </Button>
          )}

          {itemType === "invoice" && item.payment_id && item.remittance_generated && (
            <>
              <Button
                className="w-full bg-primary hover:bg-primary/90 shadow-sm font-medium tracking-tight"
                onClick={handleGenerateInvoiceRemittance}
                disabled={isLoading}
              >
                Regenerate Reconciliation
              </Button>

              <Button
                variant="outline"
                className="w-full text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-gray-900 font-medium tracking-tight mt-2"
                onClick={handleDownloadInvoiceRemittance}
                disabled={isLoading}
              >
                Download Reconciliation
              </Button>
            </>
          )}
        </div>

        {/* Format Selection (for payment reconciliation) */}
        {itemType === "payment" && (
          (item.status === "Paid" || item.status === "Sent") || item.remittance_generated
        ) && (
          <div className="space-y-5">
            <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
              Reconciliation Format
            </h4>
            <div className="flex space-x-2 bg-gray-50 p-4 rounded-lg border border-gray-100">
              {["MT940", "BAI2", "ISO20022"].map((format) => (
                <button
                  key={format}
                  className={`px-3 py-2 border rounded-lg text-sm font-medium transition-all ${
                    remittanceFormat === format
                      ? "bg-white border-primary/20 text-primary shadow-sm"
                      : "border-gray-200 text-gray-600 hover:bg-white hover:border-gray-300"
                  }`}
                  onClick={() => setRemittanceFormat(format)}
                >
                  {format}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Format Selection (for invoice reconciliation) */}
        {itemType === "invoice" && item.payment_id && (
          <div className="space-y-5">
            <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
              Reconciliation Format
            </h4>
            <div className="flex space-x-2 bg-gray-50 p-4 rounded-lg border border-gray-100">
              {["MT940", "BAI2", "ISO20022"].map((format) => (
                <button
                  key={format}
                  className={`px-3 py-2 border rounded-lg text-sm font-medium transition-all ${
                    remittanceFormat === format
                      ? "bg-white border-primary/20 text-primary shadow-sm"
                      : "border-gray-200 text-gray-600 hover:bg-white hover:border-gray-300"
                  }`}
                  onClick={() => setRemittanceFormat(format)}
                >
                  {format}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );

  // For inline mode (used in KanbanBoard)
  if (inlineMode) {
    // Using React.memo pattern to avoid unnecessary re-renders
    // The key is not included here as it's handled by the parent KanbanBoard
    return (
      <div className="relative">
        <div
          className={`p-5 h-full overflow-y-auto custom-scrollbar detail-panel-content ${
            isOpen ? "slide-in-left" : "slide-out-left"
          }`}
        >
          {contentSection}
        </div>

        {/* Payment Sending Overlay */}
        {isSendingPayment && (
          <div className="absolute inset-0 bg-white bg-opacity-80 flex items-center justify-center z-50">
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="text-sm font-medium text-gray-700">Sending payment...</p>
            </div>
          </div>
        )}
      </div>
    );
  }

  // For fullscreen slideover panel (default)
  return (
    <>
      <div
        className={`fixed top-0 right-0 w-full md:w-1/3 h-full bg-white shadow-lg z-10 overflow-y-auto custom-scrollbar ${
          isOpen ? "slide-in-left" : "slide-out-left"
        }`}
      >
        <div className="p-6 detail-panel-content">
          {contentSection}
        </div>
      </div>

      {/* Processing Overlay for blockchain transactions */}
      <ProcessingOverlay
        isOpen={isProcessing}
        title={processingTitle}
        message={processingMessage}
        duration={5000}
      />

      {/* Link Invoice Modal */}
      {isLinkInvoiceModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-lg p-6 max-w-md w-full mx-4">
            <div className="mb-4">
              <h2 className="text-xl font-medium tracking-tight text-gray-900">Link to Invoice</h2>
              <p className="text-gray-600 mt-1.5">
                Select an open invoice to link with this payment.
              </p>
            </div>

            <div className="py-4 space-y-6">
              <div className="space-y-2">
                <label className="text-xs font-medium text-gray-700 tracking-wider">Select Invoice</label>
                <Select
                  onValueChange={(value) => {
                    console.log("Selected invoice ID:", value);
                    setSelectedInvoiceId(Number(value));
                  }}
                  value={selectedInvoiceId?.toString() || ""}
                >
                  <SelectTrigger className="bg-gray-50 border border-gray-200 focus:ring-primary/20 focus:border-primary/30">
                    <SelectValue placeholder="Select an invoice" />
                  </SelectTrigger>
                  <SelectContent>
                    {openInvoices.length === 0 ? (
                      <SelectItem value="none" disabled>No open invoices available</SelectItem>
                    ) : (
                      openInvoices.map((invoice: {id: number, reference: string, amount: number}) => (
                        <SelectItem key={invoice.id} value={invoice.id.toString()}>
                          {invoice.reference} - {new Intl.NumberFormat("en-US", {
                            style: "currency",
                            currency: "USD"
                          }).format(invoice.amount)}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end gap-3 pt-2">
              <Button
                variant="outline"
                onClick={() => {
                  console.log("Cancel button clicked");
                  setIsLinkInvoiceModalOpen(false);
                }}
                className="text-gray-700 border-gray-300 hover:bg-gray-50 hover:text-gray-900 font-medium tracking-tight"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  console.log("Link Invoice button clicked with selectedInvoiceId:", selectedInvoiceId);
                  handleLinkToInvoiceSubmit();
                }}
                disabled={isLoading || !selectedInvoiceId}
                className="bg-primary hover:bg-primary/90 shadow-sm font-medium tracking-tight"
              >
                Link Invoice
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DetailPanel;