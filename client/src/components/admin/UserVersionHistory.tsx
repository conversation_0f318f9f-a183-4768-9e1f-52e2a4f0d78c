import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { UserSet, UserSetDiff } from "@/types/userImport";
import { 
  History, 
  Eye, 
  RotateCcw, 
  Users, 
  Plus, 
  Minus, 
  Edit,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react";

interface UserVersionHistoryProps {
  selectedUserSet: UserSet | null;
  onSelectUserSet: (userSet: UserSet) => void;
}

// Mock data - in production, this would come from API
const mockUserSets: UserSet[] = [
  {
    id: "us-003",
    version: "1.2.0",
    name: "SAP User Import - December 2024",
    description: "Updated user roles and added new cost center assignments",
    vendor: "SAP",
    status: "ACTIVE",
    userCount: 47,
    changeLog: "Added 3 new users, updated 5 role assignments, removed 1 inactive user",
    createdAt: "2024-12-15T10:30:00Z",
    createdBy: "<EMAIL>",
    activatedAt: "2024-12-15T11:00:00Z"
  },
  {
    id: "us-002",
    version: "1.1.0",
    name: "Oracle User Sync - November 2024",
    description: "Quarterly user role synchronization from Oracle HCM",
    vendor: "Oracle",
    status: "ARCHIVED",
    userCount: 45,
    changeLog: "Updated 8 user roles, added 2 new approvers",
    createdAt: "2024-11-20T14:15:00Z",
    createdBy: "<EMAIL>",
    activatedAt: "2024-11-20T14:30:00Z"
  },
  {
    id: "us-001",
    version: "1.0.0",
    name: "Initial User Setup",
    description: "Initial import of users from legacy system",
    status: "ARCHIVED",
    userCount: 42,
    changeLog: "Initial setup with 42 users across all roles",
    createdAt: "2024-10-01T09:00:00Z",
    createdBy: "<EMAIL>",
    activatedAt: "2024-10-01T09:30:00Z"
  }
];

// Mock diff data
const mockDiff: UserSetDiff = {
  added: [
    {
      id: "new-1",
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Doe",
      erpRoleCodes: ["F_BKPF_APP"],
      proofpayRoles: ["Approver_T1"],
      customerFilters: ["Boeing"],
      status: "ACTIVE"
    }
  ],
  removed: [
    {
      id: "old-1",
      email: "<EMAIL>",
      firstName: "Jane",
      lastName: "Smith",
      erpRoleCodes: ["F_BKPF_BUK"],
      proofpayRoles: ["AP"],
      customerFilters: ["All"],
      status: "LOCKED"
    }
  ],
  modified: [
    {
      user: {
        id: "mod-1",
        email: "<EMAIL>",
        firstName: "Maria",
        lastName: "Hughes",
        erpRoleCodes: ["F_BKPF_APP", "F_BKPF_MGR"],
        proofpayRoles: ["Approver_T2"],
        customerFilters: ["Boeing", "Mod Pizza"],
        status: "ACTIVE"
      },
      changes: [
        {
          field: "proofpayRoles",
          oldValue: ["Approver_T1"],
          newValue: ["Approver_T2"],
          type: "role_added"
        }
      ]
    }
  ],
  unchanged: [],
  summary: {
    totalChanges: 3,
    addedCount: 1,
    removedCount: 1,
    modifiedCount: 1
  }
};

export function UserVersionHistory({ selectedUserSet, onSelectUserSet }: UserVersionHistoryProps) {
  const [diffModalOpen, setDiffModalOpen] = useState(false);
  const [selectedDiff, setSelectedDiff] = useState<UserSetDiff | null>(null);
  const [rollbackConfirmOpen, setRollbackConfirmOpen] = useState(false);
  const [rollbackTarget, setRollbackTarget] = useState<UserSet | null>(null);

  const handleViewDiff = (userSet: UserSet) => {
    setSelectedDiff(mockDiff); // In production, fetch actual diff
    setDiffModalOpen(true);
  };

  const handleRollback = (userSet: UserSet) => {
    setRollbackTarget(userSet);
    setRollbackConfirmOpen(true);
  };

  const confirmRollback = async () => {
    if (!rollbackTarget) return;
    
    try {
      const response = await fetch(`/api/admin/usersets/${rollbackTarget.id}/rollback`, {
        method: 'POST',
      });
      
      if (response.ok) {
        // Refresh data and close modal
        setRollbackConfirmOpen(false);
        setRollbackTarget(null);
        onSelectUserSet(rollbackTarget);
      }
    } catch (error) {
      console.error('Rollback failed:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "ARCHIVED":
        return <Badge variant="secondary">Archived</Badge>;
      case "DRAFT":
        return <Badge variant="outline">Draft</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getVendorBadge = (vendor?: string) => {
    if (!vendor) return null;
    
    const colors = {
      SAP: "bg-blue-100 text-blue-800",
      Oracle: "bg-red-100 text-red-800",
      Dynamics365: "bg-purple-100 text-purple-800",
      NetSuite: "bg-orange-100 text-orange-800"
    };
    
    return (
      <Badge className={colors[vendor as keyof typeof colors] || "bg-gray-100 text-gray-800"}>
        {vendor}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-semibold mb-2">Version History</h4>
        <p className="text-sm text-muted-foreground">
          Track all user set versions with diff comparison and rollback capabilities.
        </p>
      </div>

      {/* Current Version Summary */}
      {selectedUserSet && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Current Active Version
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground">Version</div>
                <div className="text-lg font-semibold">{selectedUserSet.version}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Users</div>
                <div className="text-lg font-semibold">{selectedUserSet.userCount}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Vendor</div>
                <div>{getVendorBadge(selectedUserSet.vendor)}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-muted-foreground">Activated</div>
                <div className="text-sm">
                  {selectedUserSet.activatedAt 
                    ? new Date(selectedUserSet.activatedAt).toLocaleDateString()
                    : "Not activated"
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Version History Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            All Versions
          </CardTitle>
          <CardDescription>
            Complete history of user set versions with change tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Version</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Users</TableHead>
                <TableHead>Vendor</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockUserSets.map((userSet) => (
                <TableRow key={userSet.id}>
                  <TableCell>
                    <div className="font-medium">{userSet.version}</div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{userSet.name}</div>
                      {userSet.description && (
                        <div className="text-sm text-muted-foreground">
                          {userSet.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      {userSet.userCount}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getVendorBadge(userSet.vendor)}
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(userSet.status)}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {new Date(userSet.createdAt).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      by {userSet.createdBy.split('@')[0]}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleViewDiff(userSet)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      {userSet.status !== "ACTIVE" && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRollback(userSet)}
                        >
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Diff Viewer Modal */}
      <Dialog open={diffModalOpen} onOpenChange={setDiffModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Version Changes</DialogTitle>
          </DialogHeader>
          
          {selectedDiff && (
            <div className="space-y-6">
              {/* Summary */}
              <div className="grid grid-cols-4 gap-4">
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {selectedDiff.summary.addedCount}
                      </div>
                      <div className="text-sm text-muted-foreground">Added</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {selectedDiff.summary.removedCount}
                      </div>
                      <div className="text-sm text-muted-foreground">Removed</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {selectedDiff.summary.modifiedCount}
                      </div>
                      <div className="text-sm text-muted-foreground">Modified</div>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="pt-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold">
                        {selectedDiff.summary.totalChanges}
                      </div>
                      <div className="text-sm text-muted-foreground">Total Changes</div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Added Users */}
              {selectedDiff.added.length > 0 && (
                <div>
                  <h4 className="font-medium text-green-600 mb-2 flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Added Users ({selectedDiff.added.length})
                  </h4>
                  <div className="space-y-2">
                    {selectedDiff.added.map((user) => (
                      <div key={user.id} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="font-medium">{user.firstName} {user.lastName}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                        <div className="flex gap-1 mt-1">
                          {user.proofpayRoles.map((role) => (
                            <Badge key={role} variant="outline" className="text-xs">
                              {role.replace("_", " ")}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Removed Users */}
              {selectedDiff.removed.length > 0 && (
                <div>
                  <h4 className="font-medium text-red-600 mb-2 flex items-center gap-2">
                    <Minus className="h-4 w-4" />
                    Removed Users ({selectedDiff.removed.length})
                  </h4>
                  <div className="space-y-2">
                    {selectedDiff.removed.map((user) => (
                      <div key={user.id} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                        <div className="font-medium">{user.firstName} {user.lastName}</div>
                        <div className="text-sm text-muted-foreground">{user.email}</div>
                        <div className="flex gap-1 mt-1">
                          {user.proofpayRoles.map((role) => (
                            <Badge key={role} variant="outline" className="text-xs">
                              {role.replace("_", " ")}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Modified Users */}
              {selectedDiff.modified.length > 0 && (
                <div>
                  <h4 className="font-medium text-blue-600 mb-2 flex items-center gap-2">
                    <Edit className="h-4 w-4" />
                    Modified Users ({selectedDiff.modified.length})
                  </h4>
                  <div className="space-y-2">
                    {selectedDiff.modified.map((modification) => (
                      <div key={modification.user.id} className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="font-medium">
                          {modification.user.firstName} {modification.user.lastName}
                        </div>
                        <div className="text-sm text-muted-foreground mb-2">
                          {modification.user.email}
                        </div>
                        <div className="space-y-1">
                          {modification.changes.map((change, index) => (
                            <div key={index} className="text-sm">
                              <span className="font-medium">{change.field}:</span>
                              <span className="text-red-600 line-through ml-2">
                                {Array.isArray(change.oldValue) 
                                  ? change.oldValue.join(", ") 
                                  : String(change.oldValue)
                                }
                              </span>
                              <span className="text-green-600 ml-2">
                                {Array.isArray(change.newValue) 
                                  ? change.newValue.join(", ") 
                                  : String(change.newValue)
                                }
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Rollback Confirmation Modal */}
      <Dialog open={rollbackConfirmOpen} onOpenChange={setRollbackConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Rollback</DialogTitle>
          </DialogHeader>
          
          {rollbackTarget && (
            <div className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Are you sure you want to rollback to version {rollbackTarget.version}? 
                  This will replace the current active user set and cannot be undone.
                </AlertDescription>
              </Alert>
              
              <div className="p-3 bg-muted/30 rounded-lg">
                <div className="font-medium">{rollbackTarget.name}</div>
                <div className="text-sm text-muted-foreground">
                  {rollbackTarget.userCount} users • Created {new Date(rollbackTarget.createdAt).toLocaleDateString()}
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setRollbackConfirmOpen(false)}>
                  Cancel
                </Button>
                <Button variant="destructive" onClick={confirmRollback}>
                  Confirm Rollback
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
