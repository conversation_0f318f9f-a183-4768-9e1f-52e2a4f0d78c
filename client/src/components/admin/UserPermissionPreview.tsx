import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, Di<PERSON>Title } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ImportedUser } from "@/types/userImport";
import { 
  User, 
  Shield, 
  FileText, 
  Building, 
  DollarSign, 
  Users, 
  Eye,
  CheckCircle,
  AlertTriangle
} from "lucide-react";

interface UserPermissionPreviewProps {
  isOpen: boolean;
  onClose: () => void;
  user: ImportedUser | null;
}

export function UserPermissionPreview({ isOpen, onClose, user }: UserPermissionPreviewProps) {
  if (!user) return null;

  // Mock data for simulation - in production, this would be calculated based on actual rules
  const permissionData = {
    visibleDocuments: {
      invoices: user.customerFilters.includes("All") ? 1247 : user.customerFilters.length * 45,
      payments: user.customerFilters.includes("All") ? 892 : user.customerFilters.length * 32,
    },
    approvalAuthority: {
      tier: user.proofpayRoles.includes("Approver_T2") ? "T2" : 
            user.proofpayRoles.includes("Approver_T1") ? "T1" : "None",
      maxAmount: user.proofpayRoles.includes("Approver_T2") ? 100000 :
                 user.proofpayRoles.includes("Approver_T1") ? 25000 : 0,
      workflows: user.proofpayRoles.includes("Approver_T2") ? ["High Value Approval", "Standard Approval"] :
                 user.proofpayRoles.includes("Approver_T1") ? ["Standard Approval"] : []
    },
    accessScopes: {
      customers: user.customerFilters,
      legalEntities: user.legalEntityScope || [],
      costCenters: user.costCenter ? [user.costCenter] : []
    }
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case "AccountOwner":
        return "Full administrative access to all features and data";
      case "Approver_T2":
        return "Senior approval authority for high-value transactions";
      case "Approver_T1":
        return "Standard approval authority for routine transactions";
      case "AP":
        return "Accounts Payable - manage outgoing payments and vendor invoices";
      case "AR":
        return "Accounts Receivable - manage incoming payments and customer invoices";
      default:
        return "Unknown role";
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case "AccountOwner":
        return "default";
      case "Approver_T2":
        return "destructive";
      case "Approver_T1":
        return "secondary";
      case "AP":
      case "AR":
        return "outline";
      default:
        return "outline";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Permission Preview
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* User Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Name</div>
                  <div className="text-lg font-semibold">
                    {user.firstName} {user.lastName}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Email</div>
                  <div className="text-lg">{user.email}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">ERP User ID</div>
                  <div>{user.erpUserId || "N/A"}</div>
                </div>
                <div>
                  <div className="text-sm font-medium text-muted-foreground">Status</div>
                  <div className="flex items-center gap-2">
                    {user.status === "ACTIVE" ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-amber-500" />
                    )}
                    <span>{user.status}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Roles and Permissions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Roles and Permissions
              </CardTitle>
              <CardDescription>
                ProofPay roles determine what actions this user can perform
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="text-sm font-medium text-muted-foreground mb-2">ProofPay Roles</div>
                <div className="flex flex-wrap gap-2">
                  {user.proofpayRoles.map((role) => (
                    <Badge key={role} variant={getRoleBadgeVariant(role)}>
                      {role.replace("_", " ")}
                    </Badge>
                  ))}
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                {user.proofpayRoles.map((role) => (
                  <div key={role} className="p-3 bg-muted/30 rounded-lg">
                    <div className="font-medium">{role.replace("_", " ")}</div>
                    <div className="text-sm text-muted-foreground">
                      {getRoleDescription(role)}
                    </div>
                  </div>
                ))}
              </div>

              <Separator />

              <div>
                <div className="text-sm font-medium text-muted-foreground mb-2">ERP Roles</div>
                <div className="text-sm">
                  {user.erpRoleCodes.join(", ")}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Document Access */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Access
              </CardTitle>
              <CardDescription>
                Documents this user can view based on customer filters and legal entity scope
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {permissionData.visibleDocuments.invoices.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">Visible Invoices</div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      {permissionData.visibleDocuments.payments.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">Visible Payments</div>
                  </div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-3">
                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-2">Customer Filters</div>
                  <div className="flex flex-wrap gap-1">
                    {user.customerFilters.map((filter, index) => (
                      <Badge key={index} variant="outline">
                        {filter}
                      </Badge>
                    ))}
                  </div>
                </div>

                {user.legalEntityScope && user.legalEntityScope.length > 0 && (
                  <div>
                    <div className="text-sm font-medium text-muted-foreground mb-2">Legal Entity Scope</div>
                    <div className="flex flex-wrap gap-1">
                      {user.legalEntityScope.map((entity, index) => (
                        <Badge key={index} variant="outline">
                          {entity}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Approval Authority */}
          {permissionData.approvalAuthority.tier !== "None" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Approval Authority
                </CardTitle>
                <CardDescription>
                  Approval limits and workflow participation for this user
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600">
                      {permissionData.approvalAuthority.tier}
                    </div>
                    <div className="text-sm text-muted-foreground">Approval Tier</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">
                      ${permissionData.approvalAuthority.maxAmount.toLocaleString()}
                    </div>
                    <div className="text-sm text-muted-foreground">Maximum Amount</div>
                  </div>
                </div>

                <Separator />

                <div>
                  <div className="text-sm font-medium text-muted-foreground mb-2">Workflow Participation</div>
                  <div className="space-y-2">
                    {permissionData.approvalAuthority.workflows.map((workflow, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-muted/30 rounded">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{workflow}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Organizational Context */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Organizational Context
              </CardTitle>
              <CardDescription>
                Cost center, subsidiary, and other organizational attributes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {user.costCenter && (
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Cost Center</div>
                    <div>{user.costCenter}</div>
                  </div>
                )}
                {user.subsidiary && (
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">Subsidiary</div>
                    <div>{user.subsidiary}</div>
                  </div>
                )}
              </div>

              {user.attributes && Object.keys(user.attributes).length > 0 && (
                <>
                  <Separator className="my-4" />
                  <div>
                    <div className="text-sm font-medium text-muted-foreground mb-2">Additional Attributes</div>
                    <div className="space-y-1">
                      {Object.entries(user.attributes).map(([key, value]) => (
                        <div key={key} className="flex justify-between text-sm">
                          <span className="text-muted-foreground">{key}:</span>
                          <span>{String(value)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
