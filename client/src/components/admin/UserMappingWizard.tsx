import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  UserImportResult, 
  CANONICAL_USER_FIELDS, 
  ROLE_TRANSFORMATION_PRESETS,
  ProofPayRole 
} from "@/types/userImport";
import { 
  CheckCircle, 
  AlertTriangle, 
  ArrowRight, 
  Users, 
  Shield, 
  FileText,
  Ch<PERSON>ron<PERSON><PERSON><PERSON>,
  ChevronRight
} from "lucide-react";

interface UserMappingWizardProps {
  isOpen: boolean;
  onClose: () => void;
  importResult: UserImportResult | null;
  onComplete: () => void;
}

export function UserMappingWizard({ isOpen, onClose, importResult, onComplete }: UserMappingWizardProps) {
  const [step, setStep] = useState(1);
  const [mappingName, setMappingName] = useState("");
  const [fieldMappings, setFieldMappings] = useState<Record<string, string>>({});
  const [roleTransformations, setRoleTransformations] = useState<Record<string, ProofPayRole[]>>({});
  const [validationRules, setValidationRules] = useState({
    emailDomainRequired: true,
    uniqueRoleConstraint: true,
    accountOwnerRequired: true
  });

  useEffect(() => {
    if (importResult) {
      setFieldMappings(importResult.fieldMappings);
      setMappingName(`${importResult.vendor} User Mapping - ${new Date().toLocaleDateString()}`);
      
      // Load default role transformations
      const defaultTransformations = ROLE_TRANSFORMATION_PRESETS[importResult.vendor] || {};
      setRoleTransformations(defaultTransformations);
    }
  }, [importResult]);

  const handleFieldMappingChange = (vendorField: string, canonicalField: string) => {
    setFieldMappings(prev => ({
      ...prev,
      [vendorField]: canonicalField
    }));
  };

  const handleRoleTransformationChange = (erpRole: string, proofpayRoles: ProofPayRole[]) => {
    setRoleTransformations(prev => ({
      ...prev,
      [erpRole]: proofpayRoles
    }));
  };

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1);
    }
  };

  const handlePrevious = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleComplete = async () => {
    try {
      // Save the mapping configuration
      const mappingConfig = {
        name: mappingName,
        vendor: importResult?.vendor,
        fieldMappings,
        roleTransformations,
        validationRules
      };

      const response = await fetch('/api/admin/users/mappings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mappingConfig),
      });

      if (response.ok) {
        onComplete();
      } else {
        throw new Error('Failed to save mapping');
      }
    } catch (error) {
      console.error('Failed to complete mapping:', error);
    }
  };

  if (!importResult) return null;

  const vendorFields = Object.keys(importResult.fieldMappings);
  const mappedFields = Object.values(fieldMappings).filter(Boolean);
  const unmappedFields = vendorFields.filter(field => !fieldMappings[field]);
  const erpRoles = [...new Set(importResult.users.flatMap(user => user.erpRoles || []))];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>User Import Mapping Wizard</DialogTitle>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((stepNum) => (
              <div key={stepNum} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step >= stepNum ? 'bg-primary text-primary-foreground' : 'bg-gray-200 text-gray-600'
                }`}>
                  {stepNum}
                </div>
                {stepNum < 3 && (
                  <div className={`w-12 h-0.5 ${step > stepNum ? 'bg-primary' : 'bg-gray-200'}`} />
                )}
              </div>
            ))}
          </div>
          <Badge variant="outline">
            Step {step} of 3
          </Badge>
        </div>

        {/* Step 1: Review Import */}
        {step === 1 && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Review Import Results</h3>
              <p className="text-sm text-muted-foreground">
                Review the imported user data from {importResult.vendor} before proceeding with field mapping.
              </p>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Users Found
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{importResult.users.length}</div>
                  <p className="text-xs text-muted-foreground">user records</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Fields Detected
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{vendorFields.length}</div>
                  <p className="text-xs text-muted-foreground">data fields</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Roles Found
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{erpRoles.length}</div>
                  <p className="text-xs text-muted-foreground">unique roles</p>
                </CardContent>
              </Card>
            </div>

            {/* Warnings and Errors */}
            {importResult.warnings.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-1">Warnings:</div>
                  <ul className="list-disc list-inside text-sm">
                    {importResult.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {importResult.errors.length > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-1">Errors:</div>
                  <ul className="list-disc list-inside text-sm">
                    {importResult.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Sample Data Preview */}
            <div>
              <h4 className="font-medium mb-2">Sample Data Preview</h4>
              <div className="border rounded-lg overflow-hidden">
                <div className="bg-gray-50 px-4 py-2 border-b">
                  <div className="grid grid-cols-4 gap-4 text-sm font-medium">
                    <div>Email</div>
                    <div>Name</div>
                    <div>ERP Roles</div>
                    <div>Status</div>
                  </div>
                </div>
                <div className="max-h-48 overflow-y-auto">
                  {importResult.users.slice(0, 5).map((user, index) => (
                    <div key={index} className="px-4 py-2 border-b last:border-b-0">
                      <div className="grid grid-cols-4 gap-4 text-sm">
                        <div className="truncate">{user.email || 'N/A'}</div>
                        <div className="truncate">{user.firstName} {user.lastName}</div>
                        <div className="truncate">
                          {user.erpRoles?.join(', ') || 'N/A'}
                        </div>
                        <div>
                          <Badge variant={user.status === 'ACTIVE' ? 'default' : 'secondary'}>
                            {user.status || 'Unknown'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              {importResult.users.length > 5 && (
                <p className="text-xs text-muted-foreground mt-2">
                  Showing 5 of {importResult.users.length} users
                </p>
              )}
            </div>
          </div>
        )}

        {/* Step 2: Field Mapping */}
        {step === 2 && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Map Fields</h3>
              <p className="text-sm text-muted-foreground">
                Map {importResult.vendor} fields to canonical fields used by the system.
              </p>
            </div>

            {/* Mapping Progress */}
            <div className="flex items-center space-x-4 p-3 bg-muted/30 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Mapped: {mappedFields.length}</span>
              </div>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-amber-500" />
                <span className="text-sm">Unmapped: {unmappedFields.length}</span>
              </div>
            </div>

            {/* Field Mappings */}
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {vendorFields.map((vendorField) => (
                <div key={vendorField} className="flex items-center space-x-4 p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium text-sm">{vendorField}</div>
                    <div className="text-xs text-muted-foreground">
                      {importResult.vendor} field
                    </div>
                  </div>
                  
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  
                  <div className="flex-1">
                    <Select
                      value={fieldMappings[vendorField] || ""}
                      onValueChange={(value) => handleFieldMappingChange(vendorField, value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select canonical field" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">No mapping</SelectItem>
                        {Object.entries(CANONICAL_USER_FIELDS).map(([field, description]) => (
                          <SelectItem key={field} value={field}>
                            <div>
                              <div className="font-medium">{field}</div>
                              <div className="text-xs text-muted-foreground">{description}</div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {fieldMappings[vendorField] && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => handleFieldMappingChange(vendorField, "")}
                    >
                      Remove
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Step 3: Role Transformations */}
        {step === 3 && (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-2">Configure Role Transformations</h3>
              <p className="text-sm text-muted-foreground">
                Map {importResult.vendor} roles to ProofPay roles and configure validation rules.
              </p>
            </div>

            {/* Mapping Name */}
            <div>
              <Label htmlFor="mappingName">Mapping Name</Label>
              <Input
                id="mappingName"
                value={mappingName}
                onChange={(e) => setMappingName(e.target.value)}
                placeholder="Enter a name for this mapping configuration"
              />
            </div>

            {/* Role Transformations */}
            <div>
              <h4 className="font-medium mb-3">Role Transformations</h4>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {erpRoles.map((erpRole) => (
                  <div key={erpRole} className="flex items-center space-x-4 p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium text-sm">{erpRole}</div>
                      <div className="text-xs text-muted-foreground">
                        {importResult.vendor} role
                      </div>
                    </div>
                    
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    
                    <div className="flex-1">
                      <Select
                        value={roleTransformations[erpRole]?.[0] || ""}
                        onValueChange={(value) => 
                          handleRoleTransformationChange(erpRole, value ? [value as ProofPayRole] : [])
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select ProofPay role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">No mapping</SelectItem>
                          <SelectItem value="AR">AR (Accounts Receivable)</SelectItem>
                          <SelectItem value="AP">AP (Accounts Payable)</SelectItem>
                          <SelectItem value="Approver_T1">Approver Tier 1</SelectItem>
                          <SelectItem value="Approver_T2">Approver Tier 2</SelectItem>
                          <SelectItem value="AccountOwner">Account Owner</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between pt-6 border-t">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={step === 1}
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          <div className="flex space-x-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {step < 3 ? (
              <Button onClick={handleNext}>
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button onClick={handleComplete}>
                Complete Import
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
