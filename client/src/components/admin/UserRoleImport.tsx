import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { UserImportPanel } from "./UserImportPanel";
import { UserMappingWizard } from "./UserMappingWizard";
import { UserVersionHistory } from "./UserVersionHistory";
import { UserManagementGrid } from "./UserManagementGrid";
import { UserPermissionPreview } from "./UserPermissionPreview";
import { UserImportResult, UserSet, ImportedUser } from "@/types/userImport";

interface UserRoleImportProps {
  isLoading: boolean;
}

export function UserRoleImport({ isLoading }: UserRoleImportProps) {
  const [importResult, setImportResult] = useState<UserImportResult | null>(null);
  const [mappingWizardOpen, setMappingWizardOpen] = useState(false);
  const [selectedUserSet, setSelectedUserSet] = useState<UserSet | null>(null);
  const [previewUser, setPreviewUser] = useState<ImportedUser | null>(null);
  const [previewModalOpen, setPreviewModalOpen] = useState(false);

  const handleImportComplete = (result: UserImportResult) => {
    setImportResult(result);
    setMappingWizardOpen(true);
  };

  const handleMappingComplete = () => {
    setMappingWizardOpen(false);
    setImportResult(null);
    // Refresh user sets and grid
  };

  const handlePreviewPermissions = (user: ImportedUser) => {
    setPreviewUser(user);
    setPreviewModalOpen(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
          <div className="space-y-4">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Users & Roles</h3>
        <p className="text-sm text-muted-foreground">
          Import and manage users and their role-based access from ERP systems.
          Replace manual member management with automated import-driven workflows.
        </p>
      </div>

      <Tabs defaultValue="users" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="import">Import & Connectors</TabsTrigger>
          <TabsTrigger value="versions">Version History</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          <Card className="p-6">
            <UserManagementGrid 
              onPreviewPermissions={handlePreviewPermissions}
              selectedUserSet={selectedUserSet}
            />
          </Card>
        </TabsContent>

        <TabsContent value="import" className="space-y-6">
          <Card className="p-6">
            <UserImportPanel onImportComplete={handleImportComplete} />
          </Card>
        </TabsContent>

        <TabsContent value="versions" className="space-y-6">
          <Card className="p-6">
            <UserVersionHistory 
              selectedUserSet={selectedUserSet}
              onSelectUserSet={setSelectedUserSet}
            />
          </Card>
        </TabsContent>
      </Tabs>

      {/* Mapping Wizard Modal */}
      <UserMappingWizard
        isOpen={mappingWizardOpen}
        onClose={() => setMappingWizardOpen(false)}
        importResult={importResult}
        onComplete={handleMappingComplete}
      />

      {/* Permission Preview Modal */}
      <UserPermissionPreview
        isOpen={previewModalOpen}
        onClose={() => setPreviewModalOpen(false)}
        user={previewUser}
      />
    </div>
  );
}
