import React, { useState, use<PERSON><PERSON>back, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ERPVendor, UserImportResult, VALIDATION_CONSTRAINTS } from "@/types/userImport";
import { ERPUserParserFactory } from "@/services/erpUserParsers";
import { Upload, FileText, AlertTriangle, CheckCircle, X, Download, RefreshCw } from "lucide-react";

interface UserImportPanelProps {
  onImportComplete: (result: UserImportResult) => void;
}

export function UserImportPanel({ onImportComplete }: UserImportPanelProps) {
  const [selectedVendor, setSelectedVendor] = useState<ERPVendor>("SAP");
  const [files, setFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isDragActive, setIsDragActive] = useState(false);
  const [apiCredentials, setApiCredentials] = useState({
    baseUrl: "",
    apiKey: "",
    username: "",
    password: ""
  });
  const [isApiPulling, setIsApiPulling] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setIsDragActive(true);
    } else if (e.type === "dragleave") {
      setIsDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragActive(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    handleFileSelect(droppedFiles);
  }, []);

  const handleFileSelect = (selectedFiles: File[] | FileList | null) => {
    if (!selectedFiles) return;
    
    const fileArray = Array.from(selectedFiles);
    const validFiles = fileArray.filter(file => {
      const extension = '.' + file.name.split('.').pop()?.toLowerCase();
      return VALIDATION_CONSTRAINTS.SUPPORTED_FILE_FORMATS.includes(extension) &&
             file.size <= VALIDATION_CONSTRAINTS.MAX_FILE_SIZE_MB * 1024 * 1024;
    });
    
    setFiles(validFiles);
  };

  const handleFileUpload = async () => {
    if (files.length === 0) return;
    
    setIsUploading(true);
    setUploadProgress(0);
    
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        setUploadProgress((i / files.length) * 100);
        
        const content = await readFileContent(file);
        const format = file.name.split('.').pop()?.toLowerCase() || 'unknown';
        
        const result = ERPUserParserFactory.parse(content, selectedVendor, format, file.name);
        
        if (result.errors.length === 0) {
          onImportComplete(result);
        } else {
          console.error('Import errors:', result.errors);
        }
      }
      
      setUploadProgress(100);
      setFiles([]);
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleApiPull = async () => {
    setIsApiPulling(true);
    
    try {
      // Simulate API pull - in production, this would call the actual ERP APIs
      const response = await fetch(`/api/admin/users/import/${selectedVendor}/pull`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiCredentials),
      });
      
      if (response.ok) {
        const result = await response.json();
        onImportComplete(result);
      } else {
        throw new Error('API pull failed');
      }
    } catch (error) {
      console.error('API pull failed:', error);
    } finally {
      setIsApiPulling(false);
    }
  };

  const readFileContent = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(e);
      reader.readAsText(file);
    });
  };

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  const handleDownloadSample = (vendor: ERPVendor) => {
    // Download sample file for the selected vendor
    window.open(`/api/admin/users/sample/${vendor}`, '_blank');
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-semibold mb-2">Import Users & Roles</h4>
        <p className="text-sm text-muted-foreground">
          Import user data and role assignments from ERP systems via file upload or API connectors.
        </p>
      </div>

      <Tabs defaultValue="upload" className="w-full">
        <TabsList>
          <TabsTrigger value="upload">File Upload</TabsTrigger>
          <TabsTrigger value="api">API Connectors</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload User Files
              </CardTitle>
              <CardDescription>
                Upload CSV, XLSX, JSON, XML, or ZIP files containing user and role data.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Vendor Selection */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="vendor">ERP Vendor</Label>
                  <Select value={selectedVendor} onValueChange={(value) => setSelectedVendor(value as ERPVendor)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select ERP vendor" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="SAP">SAP</SelectItem>
                      <SelectItem value="Oracle">Oracle</SelectItem>
                      <SelectItem value="Dynamics365">Dynamics 365</SelectItem>
                      <SelectItem value="NetSuite">NetSuite</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={() => handleDownloadSample(selectedVendor)}
                    className="w-full"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Sample
                  </Button>
                </div>
              </div>

              {/* File Upload Zone */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  isDragActive ? "border-primary bg-primary/5" : "border-gray-300"
                }`}
                onDragEnter={handleDrag}
                onDragOver={handleDrag}
                onDragLeave={handleDrag}
                onDrop={handleDrop}
                onClick={() => !isUploading && fileInputRef.current?.click()}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".csv,.xlsx,.xls,.json,.xml,.zip"
                  onChange={(e) => handleFileSelect(e.target.files)}
                  className="hidden"
                />
                
                {files.length > 0 ? (
                  <div className="space-y-3">
                    <CheckCircle className="mx-auto h-8 w-8 text-green-500" />
                    <div className="text-sm text-gray-600">
                      {files.length} file{files.length !== 1 ? 's' : ''} selected
                    </div>
                    <div className="max-h-32 overflow-y-auto space-y-2">
                      {files.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm">
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span>{file.name}</span>
                            <Badge variant="secondary">{(file.size / 1024).toFixed(1)} KB</Badge>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={(e) => {
                              e.stopPropagation();
                              removeFile(index);
                            }}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <>
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <p className="mt-2 text-sm text-gray-600">
                      Drag and drop your user files here, or{" "}
                      <span className="text-primary cursor-pointer">browse</span>
                    </p>
                    <p className="text-xs text-gray-500">
                      Supports CSV, XLSX, JSON, XML, and ZIP files up to {VALIDATION_CONSTRAINTS.MAX_FILE_SIZE_MB}MB
                    </p>
                  </>
                )}
              </div>

              {/* Upload Progress */}
              {isUploading && (
                <div className="space-y-2">
                  <Progress value={uploadProgress} />
                  <p className="text-sm text-center text-gray-600">
                    Processing files... {Math.round(uploadProgress)}%
                  </p>
                </div>
              )}

              {/* Upload Button */}
              <Button
                onClick={handleFileUpload}
                disabled={files.length === 0 || isUploading}
                className="w-full"
              >
                {isUploading ? "Processing..." : "Import Files"}
              </Button>

              {/* File Format Info */}
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Ensure your files contain user email, first name, last name, and role information.
                  The mapping wizard will help you map vendor-specific fields to our canonical schema.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5" />
                API Connectors
              </CardTitle>
              <CardDescription>
                Configure automated imports from ERP systems via API connections.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* API Credentials */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="baseUrl">Base URL</Label>
                  <Input
                    id="baseUrl"
                    placeholder="https://your-erp-system.com"
                    value={apiCredentials.baseUrl}
                    onChange={(e) => setApiCredentials(prev => ({ ...prev, baseUrl: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="apiKey">API Key</Label>
                  <Input
                    id="apiKey"
                    type="password"
                    placeholder="Your API key"
                    value={apiCredentials.apiKey}
                    onChange={(e) => setApiCredentials(prev => ({ ...prev, apiKey: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    placeholder="Service account username"
                    value={apiCredentials.username}
                    onChange={(e) => setApiCredentials(prev => ({ ...prev, username: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="password">Password</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Service account password"
                    value={apiCredentials.password}
                    onChange={(e) => setApiCredentials(prev => ({ ...prev, password: e.target.value }))}
                  />
                </div>
              </div>

              {/* Pull Button */}
              <Button
                onClick={handleApiPull}
                disabled={!apiCredentials.baseUrl || !apiCredentials.apiKey || isApiPulling}
                className="w-full"
              >
                {isApiPulling ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Pulling Users...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Pull Users from {selectedVendor}
                  </>
                )}
              </Button>

              {/* API Info */}
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  API connectors require service account credentials with read access to user and role data.
                  Credentials are encrypted and stored securely.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
