import React, { useState, useMemo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ImportedUser, UserSet, ProofPayRole } from "@/types/userImport";
import { 
  Search, 
  Filter, 
  Eye, 
  MoreHorizontal, 
  Users, 
  Shield, 
  Building,
  AlertCircle,
  CheckCircle
} from "lucide-react";

interface UserManagementGridProps {
  onPreviewPermissions: (user: ImportedUser) => void;
  selectedUserSet: UserSet | null;
}

// Mock data - in production, this would come from API
const mockUsers: ImportedUser[] = [
  {
    id: "1",
    email: "<EMAIL>",
    firstName: "Maria",
    lastName: "Hughes",
    erpUserId: "MHUGHES",
    erpRoleCodes: ["F_BKPF_APP", "F_BKPF_MGR"],
    proofpayRoles: ["Approver_T1"],
    customerFilters: ["Boeing", "Mod Pizza"],
    legalEntityScope: ["US-WEST", "US-CENTRAL"],
    costCenter: "CC-1001",
    subsidiary: "GlobalLogi-US",
    status: "ACTIVE"
  },
  {
    id: "2",
    email: "<EMAIL>",
    firstName: "David",
    lastName: "Nguyen",
    erpUserId: "DNGUYEN",
    erpRoleCodes: ["F_BKPF_MGR", "F_BKPF_OWN"],
    proofpayRoles: ["Approver_T2", "AccountOwner"],
    customerFilters: ["Kratos Defense"],
    legalEntityScope: ["US-EAST"],
    costCenter: "CC-2001",
    subsidiary: "GlobalLogi-US",
    status: "ACTIVE"
  },
  {
    id: "3",
    email: "<EMAIL>",
    firstName: "Alex",
    lastName: "Choi",
    erpUserId: "ACHOI",
    erpRoleCodes: ["F_BKPF_BUK"],
    proofpayRoles: ["AP"],
    customerFilters: ["All"],
    costCenter: "CC-1001",
    subsidiary: "GlobalLogi-US",
    status: "ACTIVE"
  },
  {
    id: "4",
    email: "<EMAIL>",
    firstName: "Sara",
    lastName: "Wilson",
    erpUserId: "SWILSON",
    erpRoleCodes: ["F_BKPF_GST"],
    proofpayRoles: ["AR"],
    customerFilters: ["Bumble Bee Foods"],
    costCenter: "CC-3001",
    subsidiary: "GlobalLogi-US",
    status: "PENDING"
  }
];

export function UserManagementGrid({ onPreviewPermissions, selectedUserSet }: UserManagementGridProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState<string>("name");

  // In production, users would come from selectedUserSet or API
  const users = mockUsers;

  const filteredAndSortedUsers = useMemo(() => {
    let filtered = users.filter(user => {
      const matchesSearch = searchTerm === "" || 
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesRole = roleFilter === "all" || 
        user.proofpayRoles.includes(roleFilter as ProofPayRole);
      
      const matchesStatus = statusFilter === "all" || 
        user.status === statusFilter;

      return matchesSearch && matchesRole && matchesStatus;
    });

    // Sort users
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return `${a.firstName} ${a.lastName}`.localeCompare(`${b.firstName} ${b.lastName}`);
        case "email":
          return a.email.localeCompare(b.email);
        case "role":
          return a.proofpayRoles[0]?.localeCompare(b.proofpayRoles[0] || "") || 0;
        case "status":
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });

    return filtered;
  }, [users, searchTerm, roleFilter, statusFilter, sortBy]);

  const getRoleBadgeVariant = (role: ProofPayRole) => {
    switch (role) {
      case "AccountOwner":
        return "default";
      case "Approver_T2":
        return "destructive";
      case "Approver_T1":
        return "secondary";
      case "AP":
        return "outline";
      case "AR":
        return "outline";
      default:
        return "outline";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "PENDING":
        return <AlertCircle className="h-4 w-4 text-amber-500" />;
      case "LOCKED":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  // Calculate summary statistics
  const stats = {
    total: users.length,
    active: users.filter(u => u.status === "ACTIVE").length,
    pending: users.filter(u => u.status === "PENDING").length,
    locked: users.filter(u => u.status === "LOCKED").length,
    approvers: users.filter(u => u.proofpayRoles.some(r => r.includes("Approver"))).length,
    accountOwners: users.filter(u => u.proofpayRoles.includes("AccountOwner")).length
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-semibold mb-2">User Management</h4>
        <p className="text-sm text-muted-foreground">
          View and manage imported users and their role-based access permissions.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Users className="h-4 w-4" />
              Total Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.active} active, {stats.pending} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Approvers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.approvers}</div>
            <p className="text-xs text-muted-foreground">
              approval authority
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <Building className="h-4 w-4" />
              Account Owners
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.accountOwners}</div>
            <p className="text-xs text-muted-foreground">
              full access
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.locked}</div>
            <p className="text-xs text-muted-foreground">
              locked accounts
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        
        <div className="flex gap-2">
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="AccountOwner">Account Owner</SelectItem>
              <SelectItem value="Approver_T2">Approver T2</SelectItem>
              <SelectItem value="Approver_T1">Approver T1</SelectItem>
              <SelectItem value="AP">Accounts Payable</SelectItem>
              <SelectItem value="AR">Accounts Receivable</SelectItem>
            </SelectContent>
          </Select>

          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="ACTIVE">Active</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="LOCKED">Locked</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="role">Role</SelectItem>
              <SelectItem value="status">Status</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Users Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>ProofPay Roles</TableHead>
              <TableHead>ERP Roles</TableHead>
              <TableHead>Customer Filters</TableHead>
              <TableHead>Cost Center</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAndSortedUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  No users found matching your filters.
                </TableCell>
              </TableRow>
            ) : (
              filteredAndSortedUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">
                        {user.firstName} {user.lastName}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {user.email}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {user.proofpayRoles.map((role) => (
                        <Badge key={role} variant={getRoleBadgeVariant(role)}>
                          {role.replace("_", " ")}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {user.erpRoleCodes.join(", ")}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {user.customerFilters.length > 2 
                        ? `${user.customerFilters.slice(0, 2).join(", ")} +${user.customerFilters.length - 2}`
                        : user.customerFilters.join(", ")
                      }
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{user.costCenter}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getStatusIcon(user.status)}
                      <span className="text-sm">{user.status}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onPreviewPermissions(user)}>
                          <Eye className="h-4 w-4 mr-2" />
                          Preview Permissions
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Results Summary */}
      <div className="text-sm text-muted-foreground">
        Showing {filteredAndSortedUsers.length} of {users.length} users
      </div>
    </div>
  );
}
