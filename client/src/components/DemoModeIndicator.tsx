/**
 * Demo Mode Indicator Component
 *
 * Shows demo mode status and provides controls for demo functionality.
 */

import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useDemoMode } from '@/hooks/useDemoMode';
import { Activity, Clock, Users, Wifi, WifiOff, History, Trash2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

export function DemoModeIndicator() {
  const {
    isConnected,
    isDemoMode,
    demoStatus,
    getDemoSpeedSeconds,
    eventHistory,
    clearDemoHistory,
    refreshDemoStatus
  } = useDemoMode();

  const [isHistoryOpen, setIsHistoryOpen] = useState(false);

  if (!isDemoMode) {
    return null;
  }

  return (
    <div className="bg-orange-100 border-b border-orange-200 px-4 py-2">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Activity className="h-4 w-4 text-orange-600" />
            <span className="text-sm font-medium text-orange-800">Demo Mode: End-to-End Flow</span>
            <Badge variant="secondary" className="bg-orange-200 text-orange-800 text-xs">
              AP → AR
            </Badge>
          </div>

          <div className="flex items-center gap-4 text-xs text-orange-700">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{getDemoSpeedSeconds()}s delay</span>
            </div>
            <div className="flex items-center gap-1">
              {isConnected ? (
                <Wifi className="h-3 w-3 text-green-600" />
              ) : (
                <WifiOff className="h-3 w-3 text-red-600" />
              )}
              <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Latest event */}
          {eventHistory.length > 0 && (
            <div className="text-xs text-orange-700 max-w-xs truncate">
              Latest: {eventHistory[eventHistory.length - 1].description}
            </div>
          )}

          {/* Event history dialog */}
          <Dialog open={isHistoryOpen} onOpenChange={setIsHistoryOpen}>
            <DialogTrigger asChild>
              <Button variant="ghost" size="sm" className="h-7 px-2 text-orange-700 hover:bg-orange-200">
                <History className="h-3 w-3 mr-1" />
                Events ({eventHistory.length})
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Demo Event History</DialogTitle>
                <DialogDescription>
                  Real-time events from the demo payment simulator
                </DialogDescription>
              </DialogHeader>

              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  {eventHistory.length} events
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearDemoHistory}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Clear History
                </Button>
              </div>

              <ScrollArea className="h-96 w-full">
                <div className="space-y-2">
                  {eventHistory.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No events yet
                    </div>
                  ) : (
                    eventHistory.map((event, index) => (
                      <div
                        key={index}
                        className="p-3 rounded-lg border bg-card text-card-foreground"
                      >
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <p className="text-sm font-medium">
                              {event.description}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {event.type}
                            </p>
                          </div>
                          <span className="text-xs text-muted-foreground">
                            {formatDistanceToNow(event.timestamp, { addSuffix: true })}
                          </span>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
}
