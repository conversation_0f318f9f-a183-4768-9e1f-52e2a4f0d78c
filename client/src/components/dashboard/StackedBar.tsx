/**
 * Stacked Bar Chart Component for CFO Dashboard
 *
 * Reusable stacked bar chart component using Recharts for displaying
 * multi-category data with responsive design and accessibility.
 */

import React from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { motion } from "framer-motion";

interface StackedBarData {
  name: string;
  [key: string]: string | number;
}

interface StackedBarProps {
  title: string;
  description?: string;
  data: StackedBarData[];
  bars: Array<{
    dataKey: string;
    name: string;
    color: string;
  }>;
  formatValue?: (value: number) => string;
  height?: number;
  isLoading?: boolean;
  className?: string;
}

export function StackedBar({
  title,
  description,
  data = [],
  bars = [],
  formatValue,
  height = 300,
  isLoading = false,
  className
}: StackedBarProps) {
  const defaultFormatValue = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(0)}K`;
    } else {
      return `$${value.toLocaleString()}`;
    }
  };

  const valueFormatter = formatValue || defaultFormatValue;

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div
            className="w-full bg-muted animate-pulse rounded flex items-center justify-center"
            style={{ height }}
          >
            <span className="text-muted-foreground">Loading chart data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div
            className="w-full bg-muted/20 rounded flex items-center justify-center"
            style={{ height }}
          >
            <span className="text-muted-foreground">No data available</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <div style={{ height }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={data}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis
                  dataKey="name"
                  className="text-xs"
                />
                <YAxis
                  tickFormatter={valueFormatter}
                  className="text-xs"
                />
                <Tooltip
                  formatter={(value: number) => valueFormatter(value)}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--background))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px',
                    fontSize: '12px'
                  }}
                />
                <Legend />
                {bars.map((bar) => (
                  <Bar
                    key={bar.dataKey}
                    dataKey={bar.dataKey}
                    name={bar.name}
                    stackId="a"
                    fill={bar.color}
                  />
                ))}
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}

/**
 * Specialized component for Fee & FX Savings
 */
interface FeeSavingsData {
  month: string;
  feesSaved: number;
  fxSaved: number;
}

interface FeeSavingsProps {
  data: FeeSavingsData[];
  isLoading?: boolean;
}

export function FeeSavingsChart({ data, isLoading }: FeeSavingsProps) {
  const bars = [
    {
      dataKey: "feesSaved",
      name: "Fee Savings",
      color: "#3b82f6"
    },
    {
      dataKey: "fxSaved",
      name: "FX Savings",
      color: "#10b981"
    }
  ];

  // Transform data to match StackedBarData interface
  const transformedData = data.map(item => ({
    name: item.month,
    feesSaved: item.feesSaved,
    fxSaved: item.fxSaved
  }));

  return (
    <StackedBar
      title="Fee & FX Savings"
      description="Monthly savings vs legacy payment systems"
      data={transformedData}
      bars={bars}
      isLoading={isLoading}
    />
  );
}


