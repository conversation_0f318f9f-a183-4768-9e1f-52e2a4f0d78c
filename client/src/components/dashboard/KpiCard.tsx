/**
 * KPI Card Component for CFO Dashboard
 *
 * Reusable card component for displaying key performance indicators
 * with ARIA live regions for accessibility and real-time updates.
 */

import React from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";

interface KpiCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: {
    value: number;
    label: string;
    isPositive?: boolean;
  };
  icon?: LucideIcon;
  badge?: {
    text: string;
    variant?: "default" | "secondary" | "destructive" | "outline";
  };
  isLoading?: boolean;
  onClick?: () => void;
  className?: string;
  "aria-label"?: string;
}

export function KpiCard({
  title,
  value,
  subtitle,
  trend,
  icon: Icon,
  badge,
  isLoading = false,
  onClick,
  className,
  "aria-label": ariaLabel,
}: KpiCardProps) {
  const formatValue = (val: string | number) => {
    if (typeof val === "string") {
      return val; // Return string values as-is (for counts, percentages, etc.)
    }
    if (typeof val === "number") {
      if (val >= 1000000) {
        return `$${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `$${(val / 1000).toFixed(0)}K`;
      } else {
        return `$${val.toLocaleString()}`;
      }
    }
    return val;
  };

  const cardContent = (
    <Card
      className={cn(
        "h-full transition-all duration-200 hover:shadow-md",
        onClick && "cursor-pointer hover:shadow-lg",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="flex items-center space-x-2">
          {badge && (
            <Badge variant={badge.variant || "secondary"} className="text-xs">
              {badge.text}
            </Badge>
          )}
          {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-1">
          {/* Main value with loading state */}
          <div
            className="text-2xl font-bold"
            aria-live="polite"
            aria-label={ariaLabel || `${title}: ${value}`}
          >
            {isLoading ? (
              <div className="h-8 w-24 bg-muted animate-pulse rounded" />
            ) : (
              formatValue(value)
            )}
          </div>

          {/* Subtitle and trend */}
          <div className="flex items-center justify-between">
            {subtitle && (
              <p className="text-xs text-muted-foreground">
                {isLoading ? (
                  <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                ) : (
                  subtitle
                )}
              </p>
            )}

            {trend && !isLoading && (
              <div className={cn(
                "flex items-center text-xs font-medium",
                trend.isPositive ? "text-green-600" : "text-red-600"
              )}>
                <span className="mr-1">
                  {trend.isPositive ? "↗" : "↘"}
                </span>
                {trend.value}% {trend.label}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {cardContent}
    </motion.div>
  );
}

/**
 * Specialized KPI Card for stablecoin liquidity display
 */
interface StablecoinLiquidityKpiCardProps {
  stablecoinBalances: Array<{ amount: number; symbol: string }>;
  isLoading?: boolean;
  onClick?: () => void;
}

export function StablecoinLiquidityKpiCard({
  stablecoinBalances = [],
  isLoading = false,
  onClick
}: StablecoinLiquidityKpiCardProps) {
  const totalStablecoin = stablecoinBalances.reduce((sum, balance) => sum + balance.amount, 0);

  return (
    <KpiCard
      title="Stablecoin Liquidity"
      value={totalStablecoin}
      subtitle={`${stablecoinBalances.length} stablecoin wallets`}
      trend={{
        value: 2.4,
        label: "vs last week",
        isPositive: true
      }}
      isLoading={isLoading}
      onClick={onClick}
      aria-label={`Stablecoin liquidity: ${totalStablecoin.toLocaleString()} dollars across ${stablecoinBalances.length} stablecoin wallets`}
    />
  );
}

/**
 * Specialized KPI Card for exception alerts
 */
interface ExceptionKpiCardProps {
  agingApprovals: number;
  openInvoices: number;
  isLoading?: boolean;
  onClick?: () => void;
}

export function ExceptionKpiCard({
  agingApprovals = 0,
  openInvoices = 0,
  isLoading = false,
  onClick
}: ExceptionKpiCardProps) {
  const totalExceptions = agingApprovals + openInvoices;
  const hasExceptions = totalExceptions > 0;

  return (
    <KpiCard
      title="Exception Alerts"
      value={totalExceptions.toString()} // No dollar formatting for counts
      subtitle={`${agingApprovals} aging approvals, ${openInvoices} open invoices`}
      badge={{
        text: hasExceptions ? "Action Required" : "All Clear",
        variant: hasExceptions ? "destructive" : "secondary"
      }}
      isLoading={isLoading}
      onClick={onClick}
      className={hasExceptions ? "border-red-200 bg-red-50/50" : undefined}
      aria-label={`${totalExceptions} total exceptions: ${agingApprovals} aging approvals and ${openInvoices} open invoices`}
    />
  );
}
