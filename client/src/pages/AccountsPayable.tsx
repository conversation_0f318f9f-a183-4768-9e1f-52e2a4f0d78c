/**
 * Accounts Payable Module
 *
 * This page handles the Accounts Payable workflow in the application.
 * It provides a Kanban-style interface for managing outgoing payments through their lifecycle:
 * - Importing payment files
 * - Approving payments with BLS signatures
 * - Sending payments to the blockchain network
 * - Importing receipts for verification
 * - Generating reconciliation documents
 *
 * The workflow follows these stages:
 * 1. Payment is imported from a standardized file format (visible in first column)
 * 2. Payment requires approval (first column)
 * 3. Once approved with BLS signature, payment appears in both approval and payment columns
 * 4. After payment is sent to the network, it moves to "Paid" status but doesn't appear in reconciliation
 * 5. Receipt must be imported manually (Import Receipt button), which sets receipt_imported=true
 * 6. Only after receipt import does the payment appear in the reconciliation column
 * 7. Reconciliation documents can then be generated for payments with verified receipts
 *
 * The receipt import step was added to prevent all sent payments from automatically
 * appearing in the reconciliation column. This ensures proper verification before reconciliation.
 */

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import KanbanBoard, { KanbanColumn } from "@/components/KanbanBoard";
import PaymentCard from "@/components/PaymentCard";
import DetailPanel from "@/components/DetailPanel";
import ProcessingOverlay from "@/components/ProcessingOverlay";
import UploadModal from "@/components/UploadModal";
import ImportReceiptModal from "@/components/ImportReceiptModal";
import { usePayments } from "@/hooks/usePayments";
import { useToast } from "@/hooks/use-toast";
import { Upload, RefreshCw } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";

/**
 * Interface for tracking payment animations between columns
 * Used to create smooth transitions when payments change status
 *
 * @property id - Payment ID to track
 * @property sourceColumn - Column ID where the payment starts
 * @property targetColumn - Column ID where the payment is moving to
 * @property animationPhase - Current animation state (exiting source or entering target)
 */
interface TransitioningPayment {
  id: number;
  sourceColumn: string | null;
  targetColumn: string | null;
  animationPhase: 'slide-up-out' | 'slide-down-in' | null;
}

/**
 * Accounts Payable Component
 *
 * This component implements the main Accounts Payable workflow interface.
 * It manages payment approvals, blockchain transactions, and reconciliation generation.
 * The component uses a Kanban-style board layout with three columns representing payment stages.
 *
 * Features:
 * - Import payment files in standardized formats
 * - Approve/revoke approval for payments
 * - Send approved payments to blockchain network
 * - Generate reconciliation documents in multiple formats
 * - Smooth animations for state transitions between columns
 * - Detail panels that slide out when a payment is selected
 *
 * @returns React component for Accounts Payable workflow
 */
const AccountsPayable = () => {
  // Hooks for notifications and data management
  const { toast } = useToast();
  const queryClient = useQueryClient();

  /**
   * Animation and UI State Management
   * The following state variables handle complex animations when payments change status
   * They track payments as they move between columns with smooth transitions
   */

  // Track recently updated payment IDs for highlighting with animation
  const [updatedPaymentIds, setUpdatedPaymentIds] = useState<number[]>([]);

  // Track payments currently animating between columns
  const [transitioningPayments, setTransitioningPayments] = useState<TransitioningPayment[]>([]);

  // Store previous column contents to detect movements for animations
  const [prevNotApproved, setPrevNotApproved] = useState<number[]>([]);
  const [prevApproved, setPrevApproved] = useState<number[]>([]);
  const [prevSent, setPrevSent] = useState<number[]>([]);

  // Get payments data from API with automatic filtering
  const {
    notApprovedPayments,
    approvedPayments,
    sentPayments,
    approvalRequests,
    isLoading,
    error,
    sendPaymentMutation,
    approvePaymentMutation,
    revokePaymentMutation,
    generateRemittanceMutation
  } = usePayments();

  // Completely remove automatic polling to prevent any flickering
  // Actions will manually update only the affected payment

  // Force a refresh of payments data
  const refreshPayments = () => {
    queryClient.invalidateQueries({ queryKey: ["/api/payments"] });
  };

  // Helper to mark a payment as updated (for animations)
  const markPaymentUpdated = (id: number) => {
    setUpdatedPaymentIds(prev => [...prev, id]);
    // Remove the ID after animation completes
    setTimeout(() => {
      setUpdatedPaymentIds(prev => prev.filter(paymentId => paymentId !== id));
    }, 2000);
  };

  // Track payment movements between columns for animations
  useEffect(() => {
    if (isLoading) return;

    // Get current IDs in each column
    const currentNotApprovedIds = notApprovedPayments.map(p => p.id);
    const currentApprovedIds = approvedPayments.map(p => p.id);
    const currentSentIds = sentPayments.map(p => p.id);

    // Find payments that moved between columns
    const transitions: TransitioningPayment[] = [];

    // Check payments that moved from Not Approved to Approved
    prevNotApproved.forEach(id => {
      if (currentApprovedIds.includes(id) && !currentNotApprovedIds.includes(id)) {
        transitions.push({
          id,
          sourceColumn: 'not-approved',
          targetColumn: 'approved',
          animationPhase: 'slide-up-out'
        });
      }
    });

    // Check payments that moved from Approved to Sent
    prevApproved.forEach(id => {
      if (currentSentIds.includes(id) && !currentApprovedIds.includes(id)) {
        transitions.push({
          id,
          sourceColumn: 'approved',
          targetColumn: 'sent',
          animationPhase: 'slide-up-out'
        });
      }
    });

    // Start animation process if we have transitions
    if (transitions.length > 0) {
      // Set transitioning payments to slide up and out first
      setTransitioningPayments(transitions);

      // After a short delay, change animation to slide down and in
      const timer1 = setTimeout(() => {
        setTransitioningPayments(prev =>
          prev.map(tp => ({...tp, animationPhase: 'slide-down-in'}))
        );

        // Finally, clear transitions after animation completes
        const timer2 = setTimeout(() => {
          setTransitioningPayments([]);
        }, 500);

        return () => clearTimeout(timer2);
      }, 400);

      return () => clearTimeout(timer1);
    }

    // Update prev state for next comparison
    setPrevNotApproved(currentNotApprovedIds);
    setPrevApproved(currentApprovedIds);
    setPrevSent(currentSentIds);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    // Use a stable stringified representation instead of just length
    JSON.stringify(notApprovedPayments.map(p => p.id)),
    JSON.stringify(approvedPayments.map(p => p.id)),
    JSON.stringify(sentPayments.map(p => p.id)),
    isLoading
  ]);

  /**
   * UI State Management
   * The following state variables control the user interface elements:
   * - Detail panel display and positioning
   * - Blockchain processing overlay
   * - File upload modal
   */

  // Detail panel state
  const [selectedPaymentId, setSelectedPaymentId] = useState<number | null>(null); // Currently selected payment
  const [activeColumnId, setActiveColumnId] = useState<string | null>(null);      // Column that contains selected payment
  const [isPanelOpen, setIsPanelOpen] = useState(false);                          // Whether detail panel is visible

  // Blockchain processing overlay state
  const [isProcessing, setIsProcessing] = useState(false);                        // Whether processing overlay is visible
  const [processingTitle, setProcessingTitle] = useState("");                     // Title for processing overlay
  const [processingMessage, setProcessingMessage] = useState("");                 // Message for processing overlay

  // Upload modal state
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);              // Whether upload modal is visible
  const [showImportReceipt, setShowImportReceipt] = useState(false);              // Import receipt JSON modal

  /**
   * Find the full payment object that matches the selected ID
   * This combines all payments from all columns to ensure we can find the payment
   * regardless of which column it's currently in
   */
  const selectedPayment = selectedPaymentId !== null
    ? [...notApprovedPayments, ...approvedPayments, ...sentPayments].find(p => p.id === selectedPaymentId)
    : null;

  /**
   * Event Handlers
   * The following functions handle user interactions with the interface
   */

  /**
   * Handle payment card click to open/close the detail panel
   *
   * This function manages the detail panel animation and state transitions
   * - If clicking the same payment while panel is open: close the panel with animation
   * - If clicking a different payment or first click: open panel with the payment details
   * - Animations are coordinated with CSS classes and setTimeout to ensure smooth transitions
   *
   * @param id - ID of the clicked payment
   * @param columnId - ID of the column containing the payment (determines available actions)
   */
  const handleCardClick = (id: number, columnId: string) => {
    if (selectedPaymentId === id && isPanelOpen) {
      // If clicking the same payment, close the panel with animation
      // We just toggle isPanelOpen - this will trigger the slide-out animation
      // since we're using slide-in-left and slide-out-left CSS classes
      setIsPanelOpen(false);
      // Only after the animation completes (matches the animation duration in CSS),
      // clear the selection
      setTimeout(() => {
        setSelectedPaymentId(null);
        setActiveColumnId(null);
      }, 600); // Match with animation duration in CSS
    } else {
      // Direct switching between any panels with one click
      setSelectedPaymentId(id);
      setActiveColumnId(columnId);
      // Make sure the panel is open
      if (!isPanelOpen) {
        setIsPanelOpen(true);
      }
    }
  };

  /**
   * Handle closing the detail panel
   *
   * Initiates the slide-out animation by setting isPanelOpen to false
   * After animation completes, clears the selection state
   */
  const handleClosePanel = () => {
    setIsPanelOpen(false);
    setTimeout(() => {
      setSelectedPaymentId(null);
      setActiveColumnId(null);
    }, 600); // Wait for animation to complete - match with animation duration in CSS
  };

  /**
   * Handle opening the payment file upload modal
   *
   * Shows the modal dialog for importing payment files
   */
  const handleOpenUploadModal = () => {
    setIsUploadModalOpen(true);
  };

  /**
   * Handle successful file upload completion
   *
   * Displays a success toast notification
   * Note: The actual data refresh is handled by the UploadModal component
   * which invalidates the query cache to trigger a refresh
   */
  const handleUploadSuccess = () => {
    toast({
      title: "Success",
      description: "Payment file has been processed successfully.",
    });
  };

  if (error) {
    return (
      <div className="container mx-auto p-4 text-center py-10">
        <h3 className="text-xl font-semibold text-red-500 mb-2">Error Loading Data</h3>
        <p className="text-gray-600">
          {error instanceof Error ? error.message : "Failed to load payments. Please try again."}
        </p>
        <Button
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <main className="container mx-auto p-4">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Accounts Payable Pipeline</h2>
        <div className="flex space-x-2">
          <Button
            className="bg-primary hover:bg-blue-600 text-white flex items-center"
            onClick={handleOpenUploadModal}
            size="sm"
          >
            <Upload className="mr-2 h-4 w-4" />
            Import Payment Files
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-[calc(100vh-200px)]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <KanbanBoard
          activeDetailPanel={
            isPanelOpen && selectedPayment && activeColumnId
              ? {
                  columnId: activeColumnId,
                  content: (
                    <DetailPanel
                      isOpen={isPanelOpen}
                      onClose={handleClosePanel}
                      item={selectedPayment}
                      itemType="payment"
                      inlineMode={true}
                      columnId={activeColumnId}
                      paymentOperations={{
                        sendPaymentMutation,
                        approvePaymentMutation,
                        revokePaymentMutation,
                        generateRemittanceMutation
                      }}
                      onRefresh={() => {
                        // Don't close the panel, just refresh the data silently
                        refreshPayments();

                        // Mark the current payment as updated for animation
                        if (selectedPayment) {
                          markPaymentUpdated(selectedPayment.id);
                        }

                        // Instead of changing focus, just animate the panel to indicate refresh
                        const panel = document.querySelector('.detail-panel-content');
                        if (panel) {
                          panel.classList.add('pulse-animation');
                          setTimeout(() => {
                            panel.classList.remove('pulse-animation');
                          }, 1000);
                        }
                      }}
                    />
                  ),
                }
              : null
          }
        >
          {/* Approve Column */}
          <KanbanColumn title="Approve" count={approvalRequests.length} columnId="not-approved">
            {/* Render transitioning payments that are sliding up out of this column */}
            {transitioningPayments
              .filter(tp => tp.sourceColumn === 'not-approved' && tp.animationPhase === 'slide-up-out')
              .map(tp => {
                const payment = [...notApprovedPayments, ...approvedPayments, ...sentPayments]
                  .find(p => p.id === tp.id);
                if (!payment) return null;

                return (
                  <div key={`transition-${payment.id}`} className="slide-up-out">
                    <PaymentCard
                      id={payment.id}
                      reference={payment.reference}
                      status={payment.status}
                      recipient={payment.recipient}
                      amount={payment.amount}
                      dueDate={payment.due_date}
                      onClick={(id) => handleCardClick(id, "not-approved")}
                    />
                  </div>
                );
              })}

            {/* Regular payments in this column */}
            {approvalRequests.map((payment) => {
              // Skip rendering if this payment is transitioning
              if (transitioningPayments.some(tp => tp.id === payment.id)) {
                return null;
              }

              return (
                <div
                  key={payment.id}
                  className={updatedPaymentIds.includes(payment.id) ? "pulse-animation" : ""}
                >
                  <PaymentCard
                    id={payment.id}
                    reference={payment.reference}
                    status={payment.status}
                    recipient={payment.recipient}
                    amount={payment.amount}
                    dueDate={payment.due_date}
                    onClick={(id) => handleCardClick(id, "not-approved")}
                  />
                </div>
              );
            })}

            {approvalRequests.length === 0 && transitioningPayments.filter(tp => tp.sourceColumn === 'not-approved').length === 0 && (
              <div className="text-center py-6 text-gray-500 text-sm">
                No payments to approve
              </div>
            )}
          </KanbanColumn>

          {/* Payments Column - Payment history with updating statuses */}
          <KanbanColumn title="Payments" count={approvedPayments.length} columnId="approved">
            {/* Render transitioning payments that are sliding up out of this column */}
            {transitioningPayments
              .filter(tp => tp.sourceColumn === 'approved' && tp.animationPhase === 'slide-up-out')
              .map(tp => {
                const payment = [...notApprovedPayments, ...approvedPayments, ...sentPayments]
                  .find(p => p.id === tp.id);
                if (!payment) return null;

                return (
                  <div key={`transition-${payment.id}`} className="slide-up-out">
                    <PaymentCard
                      id={payment.id}
                      reference={payment.reference}
                      status={payment.status}
                      recipient={payment.recipient}
                      amount={payment.amount}
                      dueDate={payment.due_date}
                      onClick={(id) => handleCardClick(id, "approved")}
                    />
                  </div>
                );
              })}

            {/* Render transitioning payments that are sliding down into this column */}
            {transitioningPayments
              .filter(tp => tp.targetColumn === 'approved' && tp.animationPhase === 'slide-down-in')
              .map(tp => {
                const payment = [...notApprovedPayments, ...approvedPayments, ...sentPayments]
                  .find(p => p.id === tp.id);
                if (!payment) return null;

                return (
                  <div key={`transition-${payment.id}`} className="slide-down-in">
                    <PaymentCard
                      id={payment.id}
                      reference={payment.reference}
                      status={payment.status}
                      recipient={payment.recipient}
                      amount={payment.amount}
                      dueDate={payment.due_date}
                      onClick={(id) => handleCardClick(id, "approved")}
                    />
                  </div>
                );
              })}

            {/* Regular payments in this column */}
            {approvedPayments.map((payment) => {
              // Skip rendering if this payment is transitioning
              if (transitioningPayments.some(tp => tp.id === payment.id)) {
                return null;
              }

              return (
                <div
                  key={payment.id}
                  className={updatedPaymentIds.includes(payment.id) ? "pulse-animation" : ""}
                >
                  <PaymentCard
                    id={payment.id}
                    reference={payment.reference}
                    status={payment.status}
                    recipient={payment.recipient}
                    amount={payment.amount}
                    dueDate={payment.due_date}
                    onClick={(id) => handleCardClick(id, "approved")}
                  />
                </div>
              );
            })}

            {approvedPayments.length === 0 && transitioningPayments.filter(tp =>
              tp.sourceColumn === 'approved' || tp.targetColumn === 'approved').length === 0 && (
              <div className="text-center py-6 text-gray-500 text-sm">
                No approved payments
              </div>
            )}
          </KanbanColumn>

          {/* Reconciliation Column */}
          <KanbanColumn title="Reconciliation" count={sentPayments.length} columnId="sent">
            {/* Render transitioning payments that are sliding down into this column */}
            {transitioningPayments
              .filter(tp => tp.targetColumn === 'sent' && tp.animationPhase === 'slide-down-in')
              .map(tp => {
                const payment = [...notApprovedPayments, ...approvedPayments, ...sentPayments]
                  .find(p => p.id === tp.id);
                if (!payment) return null;

                return (
                  <div key={`transition-${payment.id}`} className="slide-down-in">
                    <PaymentCard
                      id={payment.id}
                      reference={payment.reference}
                      status={payment.status}
                      recipient={payment.recipient}
                      amount={payment.amount}
                      dueDate={payment.due_date}
                      onClick={(id) => handleCardClick(id, "sent")}
                    />
                  </div>
                );
              })}

            {/* Regular payments in this column */}
            {sentPayments.map((payment) => {
              // Skip rendering if this payment is transitioning
              if (transitioningPayments.some(tp => tp.id === payment.id)) {
                return null;
              }

              return (
                <div
                  key={payment.id}
                  className={updatedPaymentIds.includes(payment.id) ? "pulse-animation" : ""}
                >
                  <PaymentCard
                    id={payment.id}
                    reference={payment.reference}
                    status={payment.status}
                    recipient={payment.recipient}
                    amount={payment.amount}
                    dueDate={payment.due_date}
                    onClick={(id) => handleCardClick(id, "sent")}
                  />
                </div>
              );
            })}

            {sentPayments.length === 0 && transitioningPayments.filter(tp => tp.targetColumn === 'sent').length === 0 && (
              <div className="text-center py-6 text-gray-500 text-sm">
                No payments waiting for reconciliation
              </div>
            )}
          </KanbanColumn>
        </KanbanBoard>
      )}

      {/* Processing Overlay */}
      <ProcessingOverlay
        isOpen={isProcessing}
        title={processingTitle}
        message={processingMessage}
      />

      {/* Upload Modal */}
      <UploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onSuccess={handleUploadSuccess}
        type="payment"
      />

      {/* Import Receipt Modal */}
      <ImportReceiptModal
        isOpen={showImportReceipt}
        onClose={() => setShowImportReceipt(false)}
      />
    </main>
  );
};

export default AccountsPayable;