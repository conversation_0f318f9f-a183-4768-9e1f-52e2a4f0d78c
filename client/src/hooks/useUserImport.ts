import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "@/components/ui/use-toast";
import { 
  UserSet, 
  UserImportHistory, 
  UserFieldMapping, 
  ImportedUser,
  UserImportResult 
} from "@/types/userImport";
import { userImportService } from "@/services/userImportService";

export function useUserImport() {
  const queryClient = useQueryClient();
  
  // Fetch user sets
  const { 
    data: userSets,
    isLoading: isUserSetsLoading
  } = useQuery({
    queryKey: ['/api/admin/usersets'],
    queryFn: userImportService.getUserSets,
  });
  
  // Fetch users for active user set
  const activeUserSet = userSets?.find(us => us.status === "ACTIVE");
  
  const { 
    data: users,
    isLoading: isUsersLoading
  } = useQuery({
    queryKey: ['/api/admin/users', activeUserSet?.id],
    queryFn: () => activeUserSet ? userImportService.getUsers(activeUserSet.id) : Promise.resolve([]),
    enabled: !!activeUserSet,
  });
  
  // Fetch import history
  const { 
    data: importHistory,
    isLoading: isHistoryLoading
  } = useQuery({
    queryKey: ['/api/admin/users/import/history'],
    queryFn: userImportService.getImportHistory,
  });
  
  // Fetch field mappings
  const { 
    data: fieldMappings,
    isLoading: isMappingsLoading
  } = useQuery({
    queryKey: ['/api/admin/users/mappings'],
    queryFn: userImportService.getFieldMappings,
  });
  
  // Create user set mutation
  const createUserSetMutation = useMutation({
    mutationFn: userImportService.createUserSet,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/usersets'] });
      toast({
        title: "User set created",
        description: "The user set has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create user set",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Activate user set mutation
  const activateUserSetMutation = useMutation({
    mutationFn: userImportService.activateUserSet,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/usersets'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "User set activated",
        description: "The user set has been activated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to activate user set",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Rollback user set mutation
  const rollbackUserSetMutation = useMutation({
    mutationFn: userImportService.rollbackUserSet,
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/usersets'] });
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "User set rolled back",
        description: result.message,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to rollback user set",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Import users mutation
  const importUsersMutation = useMutation({
    mutationFn: userImportService.importUsers,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users/import/history'] });
      toast({
        title: "Users imported",
        description: "The users have been imported successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to import users",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Pull users from API mutation
  const pullUsersFromAPIMutation = useMutation({
    mutationFn: ({ vendor, credentials }: { vendor: string; credentials: Record<string, string> }) =>
      userImportService.pullUsersFromAPI(vendor, credentials),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users/import/history'] });
      toast({
        title: "Users pulled from API",
        description: "The users have been pulled from the API successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to pull users from API",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Create field mapping mutation
  const createFieldMappingMutation = useMutation({
    mutationFn: userImportService.createFieldMapping,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users/mappings'] });
      toast({
        title: "Field mapping created",
        description: "The field mapping has been created successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create field mapping",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Update field mapping mutation
  const updateFieldMappingMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<UserFieldMapping> }) =>
      userImportService.updateFieldMapping(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users/mappings'] });
      toast({
        title: "Field mapping updated",
        description: "The field mapping has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update field mapping",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Update user mutation
  const updateUserMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<ImportedUser> }) =>
      userImportService.updateUser(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "User updated",
        description: "The user has been updated successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update user",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Delete user mutation
  const deleteUserMutation = useMutation({
    mutationFn: userImportService.deleteUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
      toast({
        title: "User deleted",
        description: "The user has been deleted successfully.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete user",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Sync with RBAC mutation
  const syncWithRBACMutation = useMutation({
    mutationFn: userImportService.syncWithRBAC,
    onSuccess: (result) => {
      toast({
        title: "RBAC sync completed",
        description: `Synced ${result.syncedUsers} users successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to sync with RBAC",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Invalidate workflows mutation
  const invalidateWorkflowsMutation = useMutation({
    mutationFn: userImportService.invalidateWorkflows,
    onSuccess: (result) => {
      toast({
        title: "Workflows invalidated",
        description: `Invalidated ${result.invalidatedWorkflows} workflows successfully.`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to invalidate workflows",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  // Helper functions
  const getUserSetDiff = async (fromId: string, toId: string) => {
    try {
      return await userImportService.getUserSetDiff(fromId, toId);
    } catch (error) {
      toast({
        title: "Failed to get user set diff",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    }
  };
  
  const previewUserPermissions = async (userId: string) => {
    try {
      return await userImportService.previewUserPermissions(userId);
    } catch (error) {
      toast({
        title: "Failed to preview user permissions",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    }
  };
  
  const validateUserSet = async (userSetId: string) => {
    try {
      return await userImportService.validateUserSet(userSetId);
    } catch (error) {
      toast({
        title: "Failed to validate user set",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    }
  };
  
  const downloadSampleFile = (vendor: string) => {
    userImportService.downloadSampleFile(vendor);
  };
  
  const exportUsers = async (userSetId: string, format: "csv" | "xlsx" | "json") => {
    try {
      const blob = await userImportService.exportUsers(userSetId, format);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users_export.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "Export completed",
        description: "The users have been exported successfully.",
      });
    } catch (error) {
      toast({
        title: "Failed to export users",
        description: error.message,
        variant: "destructive",
      });
    }
  };
  
  return {
    // Data
    userSets,
    users,
    importHistory,
    fieldMappings,
    activeUserSet,
    
    // Loading states
    isLoading: isUserSetsLoading || isUsersLoading || isHistoryLoading || isMappingsLoading,
    isUserSetsLoading,
    isUsersLoading,
    isHistoryLoading,
    isMappingsLoading,
    
    // Mutations
    createUserSet: createUserSetMutation.mutate,
    activateUserSet: activateUserSetMutation.mutate,
    rollbackUserSet: rollbackUserSetMutation.mutate,
    importUsers: importUsersMutation.mutate,
    pullUsersFromAPI: pullUsersFromAPIMutation.mutate,
    createFieldMapping: createFieldMappingMutation.mutate,
    updateFieldMapping: updateFieldMappingMutation.mutate,
    updateUser: updateUserMutation.mutate,
    deleteUser: deleteUserMutation.mutate,
    syncWithRBAC: syncWithRBACMutation.mutate,
    invalidateWorkflows: invalidateWorkflowsMutation.mutate,
    
    // Helper functions
    getUserSetDiff,
    previewUserPermissions,
    validateUserSet,
    downloadSampleFile,
    exportUsers,
  };
}
