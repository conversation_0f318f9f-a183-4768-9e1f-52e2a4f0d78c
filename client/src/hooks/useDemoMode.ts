/**
 * Demo Mode Hook
 * 
 * Provides WebSocket connection for real-time demo events and demo mode utilities.
 */

import { useEffect, useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { io, Socket } from 'socket.io-client';

interface DemoEvent {
  type: string;
  data: any;
}

interface DemoStatus {
  enabled: boolean;
  speed: number;
  clientCount?: number;
  timestamp: Date;
}

export function useDemoMode() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [demoStatus, setDemoStatus] = useState<DemoStatus>({ enabled: false, speed: 3000, timestamp: new Date() });
  const [eventHistory, setEventHistory] = useState<DemoEvent[]>([]);
  const queryClient = useQueryClient();

  // Initialize WebSocket connection
  useEffect(() => {
    const newSocket = io({
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      console.log('Demo WebSocket: Connected');
      setIsConnected(true);
      
      // Request current demo status
      newSocket.emit('demo.status');
    });

    newSocket.on('disconnect', () => {
      console.log('Demo WebSocket: Disconnected');
      setIsConnected(false);
    });

    // Handle demo events
    newSocket.on('demo.event', (event: DemoEvent) => {
      console.log('Demo Event:', event);
      handleDemoEvent(event);
      
      // Add to event history
      setEventHistory(prev => [...prev.slice(-9), event]); // Keep last 10 events
    });

    // Handle demo status updates
    newSocket.on('demo.status', (status: DemoStatus) => {
      setDemoStatus(status);
    });

    // Handle event history
    newSocket.on('demo.history', (history: DemoEvent[]) => {
      setEventHistory(history);
    });

    // Handle history cleared
    newSocket.on('demo.history.cleared', () => {
      setEventHistory([]);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, []);

  // Handle different types of demo events
  const handleDemoEvent = useCallback((event: DemoEvent) => {
    switch (event.type) {
      case 'payment.sent':
        // Payment was sent - no immediate UI update needed as this is handled by optimistic updates
        break;

      case 'payment.confirmed':
        // Payment was confirmed - refresh payments data
        queryClient.invalidateQueries({ queryKey: ['/api/payments'] });
        break;

      case 'payment.failed':
        // Payment failed - refresh payments data
        queryClient.invalidateQueries({ queryKey: ['/api/payments'] });
        break;

      case 'payment.received':
        // Incoming payment received - refresh received payments and invoices
        queryClient.invalidateQueries({ queryKey: ['/api/received-payments'] });
        queryClient.invalidateQueries({ queryKey: ['/api/invoices'] });
        break;

      case 'receipt.generated':
        // Receipt was generated - refresh payments data
        queryClient.invalidateQueries({ queryKey: ['/api/payments'] });
        break;

      case 'invoice.paid':
        // Invoice was paid - refresh invoices data
        queryClient.invalidateQueries({ queryKey: ['/api/invoices'] });
        break;

      case 'reconciliation.updated':
        // Reconciliation was updated - refresh all relevant data
        queryClient.invalidateQueries({ queryKey: ['/api/payments'] });
        queryClient.invalidateQueries({ queryKey: ['/api/received-payments'] });
        queryClient.invalidateQueries({ queryKey: ['/api/invoices'] });
        break;

      default:
        console.log('Unknown demo event type:', event.type);
    }
  }, [queryClient]);

  // Check if demo mode is enabled
  const isDemoMode = demoStatus.enabled;

  // Get demo speed in seconds
  const getDemoSpeedSeconds = () => Math.round(demoStatus.speed / 1000);

  // Request demo status update
  const refreshDemoStatus = useCallback(() => {
    if (socket) {
      socket.emit('demo.status');
    }
  }, [socket]);

  // Clear demo event history
  const clearDemoHistory = useCallback(async () => {
    try {
      const response = await fetch('/api/demo/clear-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setEventHistory([]);
      }
    } catch (error) {
      console.error('Failed to clear demo history:', error);
    }
  }, []);

  // Get formatted event history for display
  const getFormattedEventHistory = useCallback(() => {
    return eventHistory.map(event => ({
      ...event,
      timestamp: new Date(event.data.timestamp),
      description: formatEventDescription(event)
    }));
  }, [eventHistory]);

  // Format event description for display
  const formatEventDescription = (event: DemoEvent): string => {
    switch (event.type) {
      case 'payment.sent':
        return `Payment #${event.data.paymentId} sent to blockchain`;
      case 'payment.confirmed':
        return `Payment #${event.data.paymentId} confirmed on blockchain`;
      case 'payment.failed':
        return `Payment #${event.data.paymentId} failed: ${event.data.error}`;
      case 'payment.received':
        return `Incoming payment received: ${event.data.amount} from ${event.data.sender}`;
      case 'receipt.generated':
        return `Receipt generated for payment #${event.data.paymentId}`;
      case 'invoice.paid':
        return `Invoice #${event.data.invoiceId} marked as paid`;
      case 'reconciliation.updated':
        return `Reconciliation updated`;
      default:
        return `${event.type}: ${JSON.stringify(event.data)}`;
    }
  };

  return {
    // Connection status
    isConnected,
    socket,

    // Demo mode status
    isDemoMode,
    demoStatus,
    getDemoSpeedSeconds,
    refreshDemoStatus,

    // Event handling
    eventHistory: getFormattedEventHistory(),
    clearDemoHistory,

    // Utilities
    formatEventDescription
  };
}
