/**
 * Demo Data Generator for CFO Dashboard
 *
 * Generates deterministic historical time-series data for dashboard metrics
 * when no real data is available. Uses faker with fixed seed for reproducible
 * demo stories.
 */

// Note: We'll use a simple deterministic random generator instead of faker
// to avoid adding another dependency

interface DemoDataPoint {
  date: string;
  value: number;
}

interface BankBalance {
  accountId: string;
  label: string;
  currency: string;
  amount: number;
}

interface StablecoinBalance {
  wallet: string;
  symbol: string;
  amount: number;
}

/**
 * Simple deterministic random number generator using seed
 */
class SeededRandom {
  private seed: number;

  constructor(seed: number = 42) {
    this.seed = seed;
  }

  next(): number {
    this.seed = (this.seed * 9301 + 49297) % 233280;
    return this.seed / 233280;
  }

  nextInt(min: number, max: number): number {
    return Math.floor(this.next() * (max - min + 1)) + min;
  }

  nextFloat(min: number, max: number): number {
    return this.next() * (max - min) + min;
  }
}

/**
 * Generate historical time series data for a specific metric
 */
export function getDemoSeries(
  metric: "paymentsProcessed" | "feesSaved" | "fxSaved" | "timeSaved" | "touchlessRate",
  days: number = 730
): DemoDataPoint[] {
  const rng = new SeededRandom(42);
  const data: DemoDataPoint[] = [];
  const today = new Date();

  // Base values and growth rates for each metric
  const metricConfig = {
    paymentsProcessed: { base: 50, growth: 0.08, volatility: 0.15 },
    feesSaved: { base: 2500, growth: 0.12, volatility: 0.20 },
    fxSaved: { base: 1800, growth: 0.10, volatility: 0.18 },
    timeSaved: { base: 120, growth: 0.15, volatility: 0.12 },
    touchlessRate: { base: 0.65, growth: 0.08, volatility: 0.05 }
  };

  const config = metricConfig[metric];

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    // Calculate trend value with year-over-year growth
    const yearProgress = (days - i) / 365;
    const trendMultiplier = Math.pow(1 + config.growth, yearProgress);

    // Add some seasonal variation (higher in Q4, lower in summer)
    const month = date.getMonth();
    const seasonalFactor = 1 + 0.1 * Math.sin((month - 6) * Math.PI / 6);

    // Add daily volatility
    const volatilityFactor = 1 + (rng.nextFloat(-1, 1) * config.volatility);

    // Calculate final value
    let value = config.base * trendMultiplier * seasonalFactor * volatilityFactor;

    // Special handling for touchless rate (percentage)
    if (metric === "touchlessRate") {
      value = Math.min(0.95, Math.max(0.30, value)); // Cap between 30% and 95%
    }

    // Round appropriately
    if (metric === "touchlessRate") {
      value = Math.round(value * 1000) / 1000; // 3 decimal places
    } else {
      value = Math.round(value);
    }

    data.push({
      date: date.toISOString().split('T')[0],
      value
    });
  }

  return data;
}

/**
 * Generate demo bank balances
 */
export function getDemoBankBalances(): BankBalance[] {
  const rng = new SeededRandom(123);

  return [
    {
      accountId: "JPM-001",
      label: "JPMorgan Chase Operating",
      currency: "USD",
      amount: Math.round(rng.nextFloat(400000, 900000))
    },
    {
      accountId: "WF-002",
      label: "Wells Fargo Treasury",
      currency: "USD",
      amount: Math.round(rng.nextFloat(200000, 500000))
    },
    {
      accountId: "HSBC-003",
      label: "HSBC International",
      currency: "EUR",
      amount: Math.round(rng.nextFloat(150000, 400000))
    }
  ];
}

/**
 * Generate demo stablecoin balances
 */
export function getDemoStablecoinBalances(): StablecoinBalance[] {
  // Return fixed $3.4M total for demo purposes
  return [
    {
      wallet: "******************************************",
      symbol: "USDC",
      amount: 2380000 // $2.38M USDC (70%)
    },
    {
      wallet: "0x8ba1f109551bD432803012645Hac136c22C85d",
      symbol: "USDT",
      amount: 1020000  // $1.02M USDT (30%)
    }
  ];
}

/**
 * Generate demo payments count for today
 */
export function getDemoPaymentsToday(): number {
  const rng = new SeededRandom(789);
  return rng.nextInt(4, 18);
}

/**
 * Calculate YTD fee savings percentage vs legacy systems
 */
export function calculateYTDFeeSavings(): number {
  // Return 82.5% as requested
  return 82.5;
}

/**
 * Get current month stablecoin settlement rate
 */
export function getCurrentStablecoinSettlementRate(): number {
  // Calculate percentage of payments settled via stablecoin vs total
  const rng = new SeededRandom(456);
  const baseRate = 72; // Base around 72%
  const variation = rng.nextFloat(-3, 3); // Small variation
  const finalRate = baseRate + variation;

  return Math.round(finalRate); // Round to whole number
}

/**
 * Generate liquidity runway projection (30 days) based on scheduled payments and invoices
 */
export function getLiquidityRunwayProjection(
  approvedPayments: any[] = [],
  openInvoices: any[] = [],
  currentStablecoinBalances?: any[]
): DemoDataPoint[] {
  const data: DemoDataPoint[] = [];
  const today = new Date();

  // Start with current stablecoin liquidity (use real balances if provided)
  const stablecoinBalances = currentStablecoinBalances || getDemoStablecoinBalances();
  const totalLiquidity = stablecoinBalances.reduce((sum, s) => sum + s.amount, 0);

  let currentLiquidity = totalLiquidity;

  console.log('Liquidity Runway Calculation:', {
    approvedPayments: approvedPayments.length,
    openInvoices: openInvoices.length,
    startingLiquidity: totalLiquidity,
    samplePayment: approvedPayments[0],
    sampleInvoice: openInvoices[0]
  });

  for (let i = 0; i < 30; i++) {
    const date = new Date(today);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    // Calculate outflows from approved payments
    const paymentsForDate = approvedPayments.filter(payment => {
      if (!payment) return false;

      // Try multiple date fields and approaches
      let targetDate = null;

      if (payment.due_date) {
        targetDate = payment.due_date;
      } else if (payment.created_at) {
        // If no due date, assume payment will be processed in 3-7 days
        const createdDate = new Date(payment.created_at);
        createdDate.setDate(createdDate.getDate() + 5);
        targetDate = createdDate.toISOString().split('T')[0];
      }

      if (!targetDate) return false;

      try {
        const paymentDate = new Date(targetDate);
        const paymentDateStr = paymentDate.toISOString().split('T')[0];
        return paymentDateStr === dateStr;
      } catch (e) {
        console.warn('Invalid payment date:', targetDate, payment);
        return false;
      }
    });

    const dailyOutflows = paymentsForDate.reduce((sum, payment) => {
      return sum + (payment.amount || 0);
    }, 0);

    // Calculate inflows from invoices
    const invoicesForDate = openInvoices.filter(invoice => {
      if (!invoice) return false;

      // Try multiple date fields
      let targetDate = null;

      if (invoice.due_date) {
        targetDate = invoice.due_date;
      } else if (invoice.created_at) {
        // If no due date, assume 30-day payment terms
        const createdDate = new Date(invoice.created_at);
        createdDate.setDate(createdDate.getDate() + 30);
        targetDate = createdDate.toISOString().split('T')[0];
      }

      if (!targetDate) return false;

      try {
        const invoiceDate = new Date(targetDate);
        const invoiceDateStr = invoiceDate.toISOString().split('T')[0];
        return invoiceDateStr === dateStr;
      } catch (e) {
        console.warn('Invalid invoice date:', targetDate, invoice);
        return false;
      }
    });

    const dailyInflows = invoicesForDate.reduce((sum, invoice) => {
      return sum + (invoice.amount || 0);
    }, 0);

    // Calculate net change
    let netChange = dailyInflows - dailyOutflows;

    // If no real data and this is the first few days, add some demo volatility
    if (netChange === 0 && approvedPayments.length === 0 && openInvoices.length === 0) {
      const rng = new SeededRandom(999 + i);
      netChange = rng.nextFloat(-15000, 5000); // Net outflow trend
    }

    currentLiquidity += netChange;

    // Log significant cash flows
    if (dailyOutflows > 0 || dailyInflows > 0) {
      console.log(`Liquidity Day ${i} (${dateStr}):`, {
        inflows: dailyInflows,
        outflows: dailyOutflows,
        net: netChange,
        balance: currentLiquidity,
        paymentsCount: paymentsForDate.length,
        invoicesCount: invoicesForDate.length
      });
    }

    data.push({
      date: dateStr,
      value: Math.round(Math.max(0, currentLiquidity)) // Don't go negative
    });
  }

  console.log('Liquidity Runway Generated:', {
    totalPoints: data.length,
    startBalance: data[0]?.value,
    endBalance: data[data.length - 1]?.value,
    hasRealData: approvedPayments.length > 0 || openInvoices.length > 0
  });

  return data;
}

/**
 * Generate time-to-settle trend (30 days rolling average)
 */
export function getTimeToSettleTrend(): DemoDataPoint[] {
  const rng = new SeededRandom(777);
  const data: DemoDataPoint[] = [];
  const today = new Date();

  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);

    // Simulate improving settlement times (trending down from ~3 days to ~1 day)
    const baseDays = 3.0 - (29 - i) * 0.05; // Gradual improvement
    const volatility = rng.nextFloat(-0.3, 0.3);
    const settleTime = Math.max(0.5, baseDays + volatility);

    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.round(settleTime * 10) / 10 // Round to 1 decimal
    });
  }

  return data;
}

/**
 * Generate demo data for fee savings chart (only up to June)
 */
export function generateFeeSavingsData() {
  const months = [
    "Jan", "Feb", "Mar", "Apr", "May", "Jun"
  ];

  const rng = new SeededRandom(789); // Use seeded random for consistency

  return months.map((month, index) => {
    // Simulate growing savings over the year
    const baseFeeSavings = 2000 + (index * 200) + rng.nextFloat(0, 500);
    const baseFxSavings = 1500 + (index * 150) + rng.nextFloat(0, 400);

    return {
      month,
      feesSaved: Math.round(baseFeeSavings),
      fxSaved: Math.round(baseFxSavings)
    };
  });
}