# ProofPay Local Development Configuration

# Database URL - PostgreSQL connection string
# For local development, you will need to create a PostgreSQL database
# Format: postgres://username:password@localhost:5432/dbname
DATABASE_URL=postgres://postgres:postgres@localhost:5432/proofpay

# BLS Private Key - Used for cryptographic signatures
# This is a demo key - for production, use a securely generated key
VITE_BLS_PRIVATE_KEY=0x0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef

# Network Simulator - Set to true to enable simulated network delays
# For local development, set to false for immediate responses
SIMULATOR_ENABLED=false

# Demo Mode - Set to true to enable full demo simulation
# This enables automatic payment processing, receipt generation, and incoming payments
DEMO_MODE=false

# Demo Speed - Simulation delay in milliseconds (1000-10000)
# Controls how fast demo events occur (default: 3000ms = 3 seconds)
DEMO_SPEED=3000

# Instructions:
# 1. Copy this file to .env in the root directory
# 2. Make sure PostgreSQL is installed and running locally
# 3. Create a database named 'proofpay':
#    createdb proofpay
# 4. Run the setup script:
#    npm run setup:db
# 5. Start the development server:
#    npm run dev:local