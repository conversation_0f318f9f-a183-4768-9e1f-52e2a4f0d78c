/**
 * Demo Incoming Payment Simulator
 * 
 * Automatically generates incoming payments for demo mode to simulate
 * the AR workflow without manual intervention.
 */

import { storage } from '../storage';
import { demoEventBus } from './demoEventBus';
import { getDemoSpeed, isDemoMode } from '../middleware/demoMode';

interface SimulatedIncomingPayment {
  sender: string;
  amount: number;
  reference: string;
  recipient: string;
}

class DemoIncomingPaymentSimulator {
  private isRunning = false;
  private intervalId: NodeJS.Timeout | null = null;
  private simulationQueue: SimulatedIncomingPayment[] = [];

  /**
   * Start the incoming payment simulator
   */
  start() {
    if (!isDemoMode() || this.isRunning) {
      return;
    }

    this.isRunning = true;
    console.log('Demo Incoming Payment Simulator: Started');

    // Run simulation every 10 seconds
    this.intervalId = setInterval(() => {
      this.processSimulationQueue();
    }, 10000);

    // Generate initial simulation data
    this.generateSimulationData();
  }

  /**
   * Stop the incoming payment simulator
   */
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    this.simulationQueue = [];
    console.log('Demo Incoming Payment Simulator: Stopped');
  }

  /**
   * Generate simulation data based on existing invoices
   */
  private async generateSimulationData() {
    try {
      // Get all open and overdue invoices
      const allInvoices = await storage.getAllInvoices();
      const unpaidInvoices = allInvoices.filter(invoice => 
        ['Open', 'Overdue'].includes(invoice.status)
      );

      // Create simulated payments for some of these invoices
      const simulationCount = Math.min(3, unpaidInvoices.length);
      const selectedInvoices = unpaidInvoices
        .sort(() => Math.random() - 0.5) // Shuffle
        .slice(0, simulationCount);

      for (const invoice of selectedInvoices) {
        const simulatedPayment: SimulatedIncomingPayment = {
          sender: this.generateRandomSender(),
          amount: invoice.amount,
          reference: invoice.reference,
          recipient: "Your Company"
        };

        this.simulationQueue.push(simulatedPayment);
      }

      console.log(`Demo Simulator: Generated ${this.simulationQueue.length} incoming payments for simulation`);

    } catch (error) {
      console.error('Error generating simulation data:', error);
    }
  }

  /**
   * Process the simulation queue
   */
  private async processSimulationQueue() {
    if (!isDemoMode() || this.simulationQueue.length === 0) {
      return;
    }

    // Process one payment from the queue
    const payment = this.simulationQueue.shift();
    if (!payment) return;

    try {
      console.log(`Demo Simulator: Processing incoming payment for reference ${payment.reference}`);

      // Create the received payment
      const receivedPayment = await storage.markPaymentReceived({
        from: payment.sender,
        amount: payment.amount,
        reference: payment.reference
      });

      if (receivedPayment) {
        // Emit real-time event
        demoEventBus.emit('payment.received', {
          paymentId: receivedPayment.id,
          amount: payment.amount,
          reference: payment.reference,
          sender: payment.sender,
          timestamp: new Date()
        });

        console.log(`Demo Simulator: Successfully created incoming payment ${receivedPayment.id}`);

        // If auto-linked to an invoice, emit additional event
        if (receivedPayment.invoice_id) {
          demoEventBus.emit('invoice.paid', {
            invoiceId: receivedPayment.invoice_id,
            paymentId: receivedPayment.id,
            amount: payment.amount,
            timestamp: new Date()
          });
        }
      }

    } catch (error) {
      console.error(`Error processing simulated incoming payment:`, error);
    }
  }

  /**
   * Generate a random sender name for simulation
   */
  private generateRandomSender(): string {
    const companies = [
      'Acme Corp',
      'Global Industries',
      'Tech Solutions Inc',
      'Manufacturing Co',
      'Service Partners',
      'Innovation Labs',
      'Enterprise Systems',
      'Digital Dynamics',
      'Future Technologies',
      'Business Solutions'
    ];

    return companies[Math.floor(Math.random() * companies.length)];
  }

  /**
   * Add a custom payment to the simulation queue
   */
  addToQueue(payment: SimulatedIncomingPayment) {
    if (isDemoMode()) {
      this.simulationQueue.push(payment);
      console.log(`Demo Simulator: Added custom payment to queue for reference ${payment.reference}`);
    }
  }

  /**
   * Get current queue status
   */
  getQueueStatus() {
    return {
      isRunning: this.isRunning,
      queueLength: this.simulationQueue.length,
      nextPayments: this.simulationQueue.slice(0, 3) // Show next 3 payments
    };
  }

  /**
   * Clear the simulation queue
   */
  clearQueue() {
    this.simulationQueue = [];
    console.log('Demo Simulator: Queue cleared');
  }

  /**
   * Restart the simulator with fresh data
   */
  async restart() {
    this.stop();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    await this.generateSimulationData();
    this.start();
  }
}

// Export singleton instance
export const demoIncomingPaymentSimulator = new DemoIncomingPaymentSimulator();
