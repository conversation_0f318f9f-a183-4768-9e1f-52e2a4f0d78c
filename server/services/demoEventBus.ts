/**
 * Demo Event Bus Service
 * 
 * Provides WebSocket-based real-time event broadcasting for demo mode.
 * Handles payment status updates, receipt generation, and reconciliation events.
 */

import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';

interface PaymentEvent {
  paymentId: number;
  txHash?: string;
  status: string;
  timestamp: Date;
  payment?: any;
  error?: string;
}

interface ReceiptEvent {
  paymentId: number;
  timestamp: Date;
}

interface IncomingPaymentEvent {
  paymentId: number;
  amount: number;
  reference: string;
  sender: string;
  timestamp: Date;
}

type DemoEvent = 
  | { type: 'payment.sent'; data: PaymentEvent }
  | { type: 'payment.confirmed'; data: PaymentEvent }
  | { type: 'payment.failed'; data: PaymentEvent }
  | { type: 'payment.received'; data: IncomingPaymentEvent }
  | { type: 'receipt.generated'; data: ReceiptEvent }
  | { type: 'reconciliation.updated'; data: any };

class DemoEventBus {
  private io: SocketIOServer | null = null;
  private eventHistory: DemoEvent[] = [];
  private maxHistorySize = 100;

  /**
   * Initialize WebSocket server
   */
  initialize(httpServer: HTTPServer) {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      },
      transports: ['websocket', 'polling']
    });

    this.io.on('connection', (socket) => {
      console.log(`Demo EventBus: Client connected ${socket.id}`);

      // Send recent event history to new clients
      socket.emit('demo.history', this.eventHistory.slice(-10));

      // Handle client requests for demo status
      socket.on('demo.status', () => {
        socket.emit('demo.status', {
          enabled: process.env.DEMO_MODE === 'true',
          speed: parseInt(process.env.DEMO_SPEED || '3000'),
          timestamp: new Date()
        });
      });

      socket.on('disconnect', () => {
        console.log(`Demo EventBus: Client disconnected ${socket.id}`);
      });
    });

    console.log('Demo EventBus: WebSocket server initialized');
  }

  /**
   * Emit a demo event to all connected clients
   */
  emit(eventType: string, data: any) {
    if (!this.io) {
      console.warn('Demo EventBus: WebSocket server not initialized');
      return;
    }

    const event: DemoEvent = {
      type: eventType as any,
      data: {
        ...data,
        timestamp: data.timestamp || new Date()
      }
    };

    // Add to history
    this.eventHistory.push(event);
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }

    // Broadcast to all clients
    this.io.emit('demo.event', event);
    
    console.log(`Demo EventBus: Emitted ${eventType}`, data);
  }

  /**
   * Emit payment sent event
   */
  emitPaymentSent(paymentId: number, txHash: string) {
    this.emit('payment.sent', {
      paymentId,
      txHash,
      status: 'PENDING'
    });
  }

  /**
   * Emit payment confirmed event
   */
  emitPaymentConfirmed(paymentId: number, txHash: string, payment: any) {
    this.emit('payment.confirmed', {
      paymentId,
      txHash,
      status: 'CONFIRMED',
      payment
    });
  }

  /**
   * Emit payment failed event
   */
  emitPaymentFailed(paymentId: number, txHash: string, error: string) {
    this.emit('payment.failed', {
      paymentId,
      txHash,
      status: 'FAILED',
      error
    });
  }

  /**
   * Emit incoming payment received event
   */
  emitPaymentReceived(paymentId: number, amount: number, reference: string, sender: string) {
    this.emit('payment.received', {
      paymentId,
      amount,
      reference,
      sender
    });
  }

  /**
   * Emit receipt generated event
   */
  emitReceiptGenerated(paymentId: number) {
    this.emit('receipt.generated', {
      paymentId
    });
  }

  /**
   * Emit reconciliation updated event
   */
  emitReconciliationUpdated(data: any) {
    this.emit('reconciliation.updated', data);
  }

  /**
   * Get event history
   */
  getEventHistory(): DemoEvent[] {
    return [...this.eventHistory];
  }

  /**
   * Clear event history
   */
  clearHistory() {
    this.eventHistory = [];
    if (this.io) {
      this.io.emit('demo.history.cleared');
    }
  }

  /**
   * Get connected client count
   */
  getClientCount(): number {
    return this.io ? this.io.sockets.sockets.size : 0;
  }

  /**
   * Broadcast demo status update
   */
  broadcastDemoStatus() {
    if (this.io) {
      this.io.emit('demo.status', {
        enabled: process.env.DEMO_MODE === 'true',
        speed: parseInt(process.env.DEMO_SPEED || '3000'),
        clientCount: this.getClientCount(),
        timestamp: new Date()
      });
    }
  }
}

// Export singleton instance
export const demoEventBus = new DemoEventBus();
