import { execSync } from 'child_process';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Initialize dotenv
dotenv.config();

// Get current file and directory paths
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

function setupProject() {
  console.log("Setting up ProofPay project...");

  try {
    // Step 1: Initialize the database with all required tables and seed data
    console.log("\n1. Initializing database...");
    execSync('node init-database.js', { stdio: 'inherit' });

    // Step 2: Ensure all migrations are applied
    console.log("\n2. Adding any additional fields...");

    // Add the receipt_imported field (it's already in the schema but run it to be safe)
    try {
      execSync('node -e "import pkg from \'pg\'; const { Pool } = pkg; const pool = new Pool({connectionString: process.env.DATABASE_URL}); pool.query(\'ALTER TABLE payments ADD COLUMN IF NOT EXISTS receipt_imported BOOLEAN NOT NULL DEFAULT false;\').then(() => { console.log(\'receipt_imported field added\'); pool.end(); }).catch(err => { console.error(err); pool.end(); });"', { stdio: 'inherit' });
    } catch (error) {
      console.log("Field may already exist, continuing...");
    }

    // Add the due_date field (it's already in the schema but run it to be safe)
    try {
      execSync('node -e "import pkg from \'pg\'; const { Pool } = pkg; const pool = new Pool({connectionString: process.env.DATABASE_URL}); pool.query(\'ALTER TABLE payments ADD COLUMN IF NOT EXISTS due_date TIMESTAMP;\').then(() => { console.log(\'due_date field added\'); pool.end(); }).catch(err => { console.error(err); pool.end(); });"', { stdio: 'inherit' });
    } catch (error) {
      console.log("Field may already exist, continuing...");
    }

    console.log("\nSetup complete! Run 'npm run dev:local' to start the application.");
  } catch (error) {
    console.error("Error during setup:", error);
  }
}

// Run the setup
setupProject();