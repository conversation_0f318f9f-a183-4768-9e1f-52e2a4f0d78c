# User Import Demo Data

This directory contains sample user data files from various ERP systems for testing the Import-Driven User & Role module.

## Files

### SAP Users (`sap_users.csv`)
- **Format**: CSV
- **Source**: SAP SUIM export
- **Fields**: BNAME (User ID), SMTP_ADDR (Email), NAME_FIRST, NAME_LAST, AGR_NAME (Role), KOSTL (Cost Center), BUKRS (Company Code), USTYP (User Type), DEPARTMENT, MANAGER
- **Sample Roles**: F_BKPF_APP (Approver), F_BKPF_MGR (Manager), F_BKPF_BUK (AP), F_BKPF_GST (AR), F_BKPF_OWN (Owner)

### Oracle Users (`oracle_users.csv`)
- **Format**: CSV
- **Source**: Oracle HCM export
- **Fields**: user_id, email, first_name, last_name, roles, cost_center, legal_entity, status, department, manager_id
- **Sample Roles**: AP_APPROVAL_LEVEL1, AP_APPROVAL_LEVEL2, AP_INVOICE_ENTRY, AR_INVOICE_ENTRY, ACCOUNT_OWNER

### Dynamics 365 Users (`d365_users.xml`)
- **Format**: XML
- **Source**: Dynamics 365 Data Management export
- **Fields**: systemuserid, internalemailaddress, firstname, lastname, roles, businessunitid, organizationid, isdisabled, department
- **Sample Roles**: Purchasing manager, Finance manager, Accounts payable clerk, Accounts receivable clerk, Account owner

### NetSuite Users (`netsuite_users.json`)
- **Format**: JSON
- **Source**: NetSuite SuiteQL export
- **Fields**: id, email, firstname, lastname, roles, department, subsidiary, isinactive, costcenter, manager
- **Sample Roles**: Approver, Senior Approver, AP Clerk, AR Clerk, Administrator

## Role Mappings

The following role transformations are applied during import:

### SAP → ProofPay
- `F_BKPF_BUK` → `AP` (Accounts Payable)
- `F_BKPF_GST` → `AR` (Accounts Receivable)
- `F_BKPF_APP` → `Approver_T1` (Approver Tier 1)
- `F_BKPF_MGR` → `Approver_T2` (Approver Tier 2)
- `F_BKPF_OWN` → `AccountOwner` (Account Owner)

### Oracle → ProofPay
- `AP_INVOICE_ENTRY` → `AP`
- `AR_INVOICE_ENTRY` → `AR`
- `AP_APPROVAL_LEVEL1` → `Approver_T1`
- `AP_APPROVAL_LEVEL2` → `Approver_T2`
- `ACCOUNT_OWNER` → `AccountOwner`

### Dynamics365 → ProofPay
- `Accounts payable clerk` → `AP`
- `Accounts receivable clerk` → `AR`
- `Purchasing manager` → `Approver_T1`
- `Finance manager` → `Approver_T2`
- `Account owner` → `AccountOwner`

### NetSuite → ProofPay
- `AP Clerk` → `AP`
- `AR Clerk` → `AR`
- `Approver` → `Approver_T1`
- `Senior Approver` → `Approver_T2`
- `Administrator` → `AccountOwner`

## Customer Filters

Based on the role and department, users are assigned customer filters:

- **Finance Department**: Boeing, Mod Pizza
- **Accounting Department**: Kratos Defense, Bumble Bee Foods
- **Executive Department**: All (full access)

## Legal Entity Scope

Users are assigned legal entity scope based on their cost center:

- **CC-1xxx**: US-WEST
- **CC-2xxx**: US-EAST
- **CC-3xxx**: US-CENTRAL
- **CC-0xxx**: All entities (executive level)

## Usage

1. Navigate to Admin > Users & Roles > Import & Connectors
2. Select the appropriate ERP vendor
3. Upload one of these demo files
4. Follow the mapping wizard to configure field mappings
5. Review and complete the import

## Testing Scenarios

### Scenario 1: SAP Import
- Upload `sap_users.csv`
- Test field mapping for SAP-specific fields
- Verify role transformations
- Check cost center assignments

### Scenario 2: Multi-Vendor Import
- Import users from different vendors
- Test version history and diff viewing
- Practice rollback functionality

### Scenario 3: API Simulation
- Use the API connector with mock credentials
- Test automated import workflows
- Verify error handling and validation

## Validation Rules

The demo data is designed to test various validation scenarios:

- **Email Domain**: All emails use @globallogistics.com domain
- **Unique Roles**: No user has conflicting roles (e.g., both Approver_T1 and Approver_T2)
- **Account Owner**: At least one user has AccountOwner role
- **Hierarchical Structure**: Manager relationships are maintained
- **Status Validation**: All users have valid status values

## Expected Results

After importing all demo files, you should have:

- **Total Users**: ~30 unique users across all vendors
- **Role Distribution**: 
  - 1 Account Owner
  - 2-3 Approver T2
  - 4-5 Approver T1
  - 8-10 AP users
  - 6-8 AR users
- **Cost Centers**: 10+ different cost centers
- **Legal Entities**: 3 main entities (US-WEST, US-EAST, US-CENTRAL)
- **Customer Filters**: 4 main customer groups plus "All" access
