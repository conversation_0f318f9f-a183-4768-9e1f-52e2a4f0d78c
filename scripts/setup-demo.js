#!/usr/bin/env node

/**
 * Demo Mode Setup Script
 * 
 * Configures the application for demo mode by updating environment variables
 * and providing helpful instructions.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');
const envPath = path.join(rootDir, '.env');
const envExamplePath = path.join(rootDir, 'server', '.env.example');

console.log('🎭 ProofPay Demo Mode Setup\n');

// Check if .env file exists
if (!fs.existsSync(envPath)) {
  console.log('📄 Creating .env file from template...');
  
  if (fs.existsSync(envExamplePath)) {
    fs.copyFileSync(envExamplePath, envPath);
    console.log('✅ .env file created\n');
  } else {
    console.error('❌ .env.example file not found');
    process.exit(1);
  }
}

// Read current .env file
let envContent = fs.readFileSync(envPath, 'utf8');

// Update demo mode settings
console.log('⚙️  Configuring demo mode settings...');

// Enable demo mode
if (envContent.includes('DEMO_MODE=')) {
  envContent = envContent.replace(/DEMO_MODE=.*/g, 'DEMO_MODE=true');
} else {
  envContent += '\nDEMO_MODE=true';
}

// Set demo speed
if (envContent.includes('DEMO_SPEED=')) {
  envContent = envContent.replace(/DEMO_SPEED=.*/g, 'DEMO_SPEED=3000');
} else {
  envContent += '\nDEMO_SPEED=3000';
}

// Disable regular simulator to avoid conflicts
if (envContent.includes('SIMULATOR_ENABLED=')) {
  envContent = envContent.replace(/SIMULATOR_ENABLED=.*/g, 'SIMULATOR_ENABLED=false');
} else {
  envContent += '\nSIMULATOR_ENABLED=false';
}

// Write updated .env file
fs.writeFileSync(envPath, envContent);

console.log('✅ Demo mode configuration updated\n');

// Display current configuration
console.log('📋 Current Demo Configuration:');
console.log('   DEMO_MODE=true');
console.log('   DEMO_SPEED=3000 (3 seconds)');
console.log('   SIMULATOR_ENABLED=false\n');

// Provide instructions
console.log('🚀 Next Steps:');
console.log('   1. Start the application:');
console.log('      npm run dev\n');
console.log('   2. Open your browser to http://localhost:5001\n');
console.log('   3. Look for the orange "Demo Mode" indicator in the top-right\n');
console.log('   4. Try the demo workflow:');
console.log('      • Go to Accounts Payable');
console.log('      • Upload a payment file');
console.log('      • Approve and send the payment');
console.log('      • Watch automatic processing in real-time\n');

console.log('💡 Demo Features:');
console.log('   • Automatic payment confirmation (3s delay)');
console.log('   • Auto-receipt import (5s after confirmation)');
console.log('   • Simulated incoming payments in AR');
console.log('   • Real-time WebSocket updates');
console.log('   • No external services required\n');

console.log('⚠️  Important:');
console.log('   • Demo mode is for demonstrations only');
console.log('   • Never enable in production');
console.log('   • Uses mock blockchain transactions\n');

console.log('🎯 Demo mode setup complete! Happy demonstrating! 🎉');
