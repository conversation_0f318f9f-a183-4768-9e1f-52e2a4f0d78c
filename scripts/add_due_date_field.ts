import pkg from 'pg';
const { Pool } = pkg;
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * This script adds the due_date field to the payments table
 *
 * The due_date field is used to store the payment due date extracted
 * from imported payment files, replacing the file_type display in the UI.
 */
async function addDueDateField() {
  const pool = new Pool({ connectionString: process.env.DATABASE_URL });

  try {
    console.log('Adding due_date field to the payments table...');

    // Check if due_date column exists
    const dueDateResult = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'payments' AND column_name = 'due_date'
    `);

    if (dueDateResult.rows.length === 0) {
      console.log('Adding due_date column...');
      await pool.query(`ALTER TABLE payments ADD COLUMN due_date TIMESTAMP`);

      // Update existing payments with a default due date (30 days from creation)
      console.log('Setting default due dates for existing payments...');
      await pool.query(`
        UPDATE payments
        SET due_date = created_at + INTERVAL '30 days'
        WHERE due_date IS NULL
      `);

      console.log('Successfully added due_date field and updated existing payments');
    } else {
      console.log('due_date column already exists.');
    }

    console.log('All changes completed successfully!');
  } catch (error) {
    console.error('Error adding due_date field:', error);
  } finally {
    await pool.end();
  }
}

addDueDateField().catch(console.error);
