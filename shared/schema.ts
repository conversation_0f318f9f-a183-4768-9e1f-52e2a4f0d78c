/**
 * Database Schema Definitions
 *
 * This file defines the database schema for the entire application using Drizzle ORM.
 * It includes table definitions, relationships, and Zod validation schemas for data insertion.
 * These schemas are shared between the client and server to ensure type safety and data integrity.
 */

import { pgTable, text, serial, integer, boolean, timestamp, doublePrecision } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

/**
 * User Model
 *
 * Basic user authentication model for the application.
 * Currently only used for very basic authentication purposes.
 */
export const users = pgTable("users", {
  id: serial("id").primaryKey(),              // Unique user identifier
  username: text("username").notNull().unique(), // Unique username for login
  password: text("password").notNull(),       // Password (should be stored as a hash in production)
});

// Schema for user creation with Zod validation
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

// Type definitions for TypeScript type safety
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

/**
 * Payment Model
 *
 * Represents outgoing payments in the Accounts Payable workflow.
 * Tracks the full lifecycle of a payment from import through approval,
 * sending via blockchain, and reconciliation.
 */
export const payments = pgTable("payments", {
  id: serial("id").primaryKey(),                      // Unique payment identifier
  reference: text("reference").notNull(),             // Reference number (must be unique)
  amount: doublePrecision("amount").notNull(),        // Payment amount
  sender: text("sender").notNull(),                   // Company sending the payment
  recipient: text("recipient").notNull(),             // Payment recipient
  recipient_address: text("recipient_address"),       // Optional shipping/billing address
  recipient_account: text("recipient_account"),       // Optional account number
  status: text("status").notNull().default("Not Approved"), // Payment status (Not Approved, Approved, Paid, Reconciled)
  file_type: text("file_type").notNull(),             // Format of import file (PEXR2002, MT103, ISO20022)
  approved: boolean("approved").notNull().default(false), // Flag for approval status
  approved_at: timestamp("approved_at"),              // When payment was approved
  sent_at: timestamp("sent_at"),                      // When payment was sent to blockchain
  file_content: text("file_content").notNull(),       // Original imported file content
  remittance_generated: boolean("remittance_generated").notNull().default(false), // Whether reconciliation file was generated
  remittance_generated_at: timestamp("remittance_generated_at"), // When reconciliation was generated
  remittance_id: integer("remittance_id"),            // ID of linked reconciliation record
  signature: text("signature"),                       // BLS signature for the payment
  message: text("message"),                           // Message hash that was signed
  receipt_imported: boolean("receipt_imported").notNull().default(false), // Flag for tracking manual receipt imports; used to control when payments appear in reconciliation column
  created_at: timestamp("created_at").defaultNow().notNull(), // Creation timestamp
});

/**
 * Schema for creating new payments
 * Excludes system-generated fields that shouldn't be provided during creation
 */
export const insertPaymentSchema = createInsertSchema(payments).omit({
  id: true,                 // Auto-generated primary key
  status: true,             // System-managed status
  approved: true,           // System-managed approval flag
  sent_at: true,            // System-managed timestamp
  remittance_generated: true, // System-managed flag
  remittance_id: true,      // System-managed relation
  created_at: true,         // Auto-generated timestamp
  receipt_imported: true,   // System-managed flag for tracking receipt imports
});

// Type definitions for TypeScript type safety
export type InsertPayment = z.infer<typeof insertPaymentSchema>;
export type Payment = typeof payments.$inferSelect;

/**
 * Reconciliation/Remittance Model
 *
 * Represents reconciliation documents generated for payments or invoices.
 * These files provide standardized financial records in various formats.
 * Previously called "Remittance" in code but displayed as "Reconciliation" to users.
 */
export const remittances = pgTable("remittances", {
  id: serial("id").primaryKey(),                      // Unique reconciliation identifier
  payment_id: integer("payment_id").notNull(),        // ID of related payment or invoice
  status: text("status").notNull().default("Generated"), // Reconciliation status
  format: text("format").notNull(),                   // File format (MT940, BAI2, ISO20022)
  created_at: timestamp("created_at").defaultNow().notNull(), // Creation timestamp
  file_path: text("file_path").notNull(),             // Location of generated file
  amount: doublePrecision("amount").notNull(),        // Transaction amount
  sender: text("sender").notNull(),                   // Payment sender
  recipient: text("recipient").notNull(),             // Payment recipient
  reference: text("reference").notNull(),             // Original payment reference
});

/**
 * Schema for creating new reconciliation records
 * Excludes system-generated fields that shouldn't be provided during creation
 */
export const insertRemittanceSchema = createInsertSchema(remittances).omit({
  id: true,                 // Auto-generated primary key
  status: true,             // System-managed status
  created_at: true,         // Auto-generated timestamp
});

// Type definitions for TypeScript type safety
export type InsertRemittance = z.infer<typeof insertRemittanceSchema>;
export type Remittance = typeof remittances.$inferSelect;

/**
 * Invoice Model
 *
 * Represents invoices in the Accounts Receivable workflow.
 * Tracks lifecycle from creation through payment receipt and reconciliation.
 * Invoices can be created manually or imported from standardized file formats.
 */
export const invoices = pgTable("invoices", {
  id: serial("id").primaryKey(),                      // Unique invoice identifier
  customer: text("customer").notNull(),               // Customer/client name
  amount: doublePrecision("amount").notNull(),        // Invoice amount
  reference: text("reference").notNull(),             // Invoice reference number
  description: text("description").notNull(),         // Invoice description/details
  due_date: timestamp("due_date").notNull(),          // Invoice payment due date
  status: text("status").notNull().default("Open"),   // Invoice status (Open, Overdue, Paid, Reconciled)
  file_type: text("file_type"),                       // Format of import file (if imported)
  file_content: text("file_content"),                 // Original imported file content (if imported)
  created_at: timestamp("created_at").defaultNow().notNull(), // Creation timestamp
  payment_id: integer("payment_id"),                  // ID of linked received payment (if linked)
  remittance_generated: boolean("remittance_generated").notNull().default(false), // Whether reconciliation was generated
  remittance_generated_at: timestamp("remittance_generated_at"), // When reconciliation was generated
  remittance_id: integer("remittance_id"),            // ID of linked reconciliation record
});

/**
 * Schema for creating new invoices
 * Excludes system-generated fields that shouldn't be provided during creation
 */
export const insertInvoiceSchema = createInsertSchema(invoices).omit({
  id: true,                 // Auto-generated primary key
  status: true,             // System-managed status
  created_at: true,         // Auto-generated timestamp
  payment_id: true,         // System-managed relation
  remittance_id: true,      // System-managed relation
});

// Type definitions for TypeScript type safety
export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;
export type Invoice = typeof invoices.$inferSelect;

/**
 * ReceivedPayment Model
 *
 * Represents incoming payments in the Accounts Receivable workflow.
 * Payments can be linked to specific invoices for reconciliation purposes.
 * Status tracking includes Unlinked, Linked, and Reconciled states.
 */
export const receivedPayments = pgTable("received_payments", {
  id: serial("id").primaryKey(),                      // Unique received payment identifier
  sender: text("sender").notNull(),                   // Payment sender name
  amount: doublePrecision("amount").notNull(),        // Payment amount
  reference: text("reference").notNull(),             // Payment reference number
  created_at: timestamp("created_at").defaultNow().notNull(), // Receipt timestamp
  invoice_id: integer("invoice_id"),                  // ID of linked invoice (if linked)
  status: text("status").notNull().default("Received"), // Payment status (Unlinked, Linked, Reconciled)
  recipient: text("recipient").notNull().default("Your Company"), // Payment recipient (your company)
  remittance_generated: boolean("remittance_generated").notNull().default(false), // Whether reconciliation was generated
  remittance_generated_at: timestamp("remittance_generated_at"), // When reconciliation was generated
  remittance_id: integer("remittance_id"),            // ID of linked reconciliation record
});

/**
 * Schema for creating new received payments
 * Excludes system-generated fields that shouldn't be provided during creation
 */
export const insertReceivedPaymentSchema = createInsertSchema(receivedPayments).omit({
  id: true,                 // Auto-generated primary key
  created_at: true,         // Auto-generated timestamp
  status: true,             // System-managed status
  remittance_generated: true, // System-managed flag
  remittance_id: true,      // System-managed relation
});

// Type definitions for TypeScript type safety
export type InsertReceivedPayment = z.infer<typeof insertReceivedPaymentSchema>;
export type ReceivedPayment = typeof receivedPayments.$inferSelect;

/**
 * File Format Definitions
 *
 * Defines the supported file formats for different types of financial documents.
 * These are used for validation during file imports and for format selection in exports.
 */
export const fileFormats = {
  payment: ["PEXR2002", "MT103", "ISO20022"],     // Supported payment file formats
  invoice: ["EDI X12", "ISO20022"],               // Supported invoice file formats
  remittance: ["MT940", "BAI2", "ISO20022"],      // Supported reconciliation file formats
} as const;

// Type definitions for TypeScript type safety with file formats
export type PaymentFileFormat = typeof fileFormats.payment[number];
export type InvoiceFileFormat = typeof fileFormats.invoice[number];
export type RemittanceFileFormat = typeof fileFormats.remittance[number];

/**
 * Workflow Engine Tables
 *
 * These tables support the pluggable workflow engine that replaces
 * the static approval predicate with dynamic, rule-based workflows
 * imported from various ERP systems.
 */

/**
 * Workflow Definitions Table
 *
 * Stores the main workflow definitions with their rules and steps.
 * Each workflow can have multiple versions for change tracking.
 */
export const workflowDefinitions = pgTable("workflow_definitions", {
  id: text("id").primaryKey(),                           // UUID for workflow
  name: text("name").notNull(),                          // Human-readable name
  description: text("description"),                      // Optional description
  version: text("version").notNull(),                    // Semantic version (e.g., "1.0.0")
  documentType: text("document_type").notNull(),         // INVOICE, PAYMENT, etc.
  status: text("status").notNull().default("ACTIVE"),   // ACTIVE, INACTIVE, DRAFT
  definition: text("definition").notNull(),              // JSON serialized WorkflowDefinition
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: text("created_by").notNull(),               // User ID who created
  sourceVendor: text("source_vendor"),                   // SAP, Oracle, etc. (if imported)
  sourceImportedAt: timestamp("source_imported_at"),     // When imported from ERP
  sourceMappingId: text("source_mapping_id"),            // Reference to field mapping used
});

/**
 * Field Mappings Table
 *
 * Stores mappings between ERP vendor-specific fields and canonical fields.
 * Used during import to translate vendor formats to our standard schema.
 */
export const fieldMappings = pgTable("field_mappings", {
  id: text("id").primaryKey(),                           // UUID for mapping
  name: text("name").notNull(),                          // Human-readable name
  vendor: text("vendor").notNull(),                      // SAP, Oracle, Dynamics365, NetSuite
  mappings: text("mappings").notNull(),                  // JSON object: vendor_field -> canonical_field
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

/**
 * Import Connectors Table
 *
 * Configuration for automated imports from ERP systems.
 * Supports both API polling and file-based imports.
 */
export const importConnectors = pgTable("import_connectors", {
  id: text("id").primaryKey(),                           // UUID for connector
  name: text("name").notNull(),                          // Human-readable name
  vendor: text("vendor").notNull(),                      // ERP vendor
  type: text("type").notNull(),                          // API or FILE
  config: text("config").notNull(),                      // JSON configuration
  lastSync: timestamp("last_sync"),                      // Last successful sync
  status: text("status").notNull().default("ACTIVE"),   // ACTIVE, ERROR, DISABLED
  errorMessage: text("error_message"),                   // Last error if any
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

/**
 * Workflow Versions Table
 *
 * Tracks all versions of workflow definitions for audit and rollback.
 * Enables diff viewing and version management.
 */
export const workflowVersions = pgTable("workflow_versions", {
  id: text("id").primaryKey(),                           // UUID for version record
  workflowId: text("workflow_id").notNull(),             // Reference to workflow
  version: text("version").notNull(),                    // Version string
  definition: text("definition").notNull(),              // Full workflow definition JSON
  changeLog: text("change_log"),                         // Optional change description
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: text("created_by").notNull(),               // User who created this version
});

/**
 * Workflow Evaluations Table
 *
 * Audit log of all workflow evaluations for compliance and debugging.
 * Tracks performance metrics and evaluation results.
 */
export const workflowEvaluations = pgTable("workflow_evaluations", {
  id: text("id").primaryKey(),                           // UUID for evaluation
  workflowId: text("workflow_id").notNull(),             // Which workflow was evaluated
  documentId: text("document_id").notNull(),             // Document that was evaluated
  documentType: text("document_type").notNull(),         // Type of document
  result: text("result").notNull(),                      // JSON serialized EvaluationResult
  evaluationTimeMs: integer("evaluation_time_ms").notNull(), // Performance metric
  evaluatedAt: timestamp("evaluated_at").defaultNow().notNull(),
  evaluatedBy: text("evaluated_by"),                     // User or system that triggered
});

/**
 * Document Approvals Table
 *
 * Tracks the approval status of documents through workflow steps.
 * Links to the existing payments/invoices tables.
 */
export const documentApprovals = pgTable("document_approvals", {
  id: text("id").primaryKey(),                           // UUID for approval record
  documentId: text("document_id").notNull(),             // Reference to payment/invoice
  documentType: text("document_type").notNull(),         // INVOICE, PAYMENT, etc.
  workflowId: text("workflow_id").notNull(),             // Which workflow is being followed
  stepId: text("step_id").notNull(),                     // Current workflow step
  stepSequence: integer("step_sequence").notNull(),      // Step order number
  status: text("status").notNull().default("PENDING"),  // PENDING, APPROVED, REJECTED, TIMEOUT
  approverUserId: text("approver_user_id"),              // Who approved/rejected
  approvedAt: timestamp("approved_at"),                  // When approved/rejected
  comments: text("comments"),                            // Optional approval comments
  timeoutAt: timestamp("timeout_at"),                    // When this step times out
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Schema for creating workflow definitions
export const insertWorkflowDefinitionSchema = createInsertSchema(workflowDefinitions).omit({
  createdAt: true,
  updatedAt: true,
});

// Schema for creating field mappings
export const insertFieldMappingSchema = createInsertSchema(fieldMappings).omit({
  createdAt: true,
  updatedAt: true,
});

// Schema for creating import connectors
export const insertImportConnectorSchema = createInsertSchema(importConnectors).omit({
  createdAt: true,
  updatedAt: true,
  lastSync: true,
  errorMessage: true,
});

/**
 * User Sets Table
 *
 * Stores versioned sets of imported users and their roles.
 * Each import creates a new user set version for rollback capability.
 */
export const userSets = pgTable("user_sets", {
  id: text("id").primaryKey(),                           // UUID for user set
  version: text("version").notNull(),                    // Version string (e.g., "1.0.0")
  name: text("name").notNull(),                          // Human-readable name
  description: text("description"),                      // Optional description
  vendor: text("vendor"),                                // ERP vendor if imported
  status: text("status").notNull().default("DRAFT"),    // DRAFT, ACTIVE, ARCHIVED
  userCount: integer("user_count").notNull().default(0), // Number of users in this set
  changeLog: text("change_log"),                         // Summary of changes
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: text("created_by").notNull(),               // User who created this set
  activatedAt: timestamp("activated_at"),                // When this set became active
});

/**
 * Imported Users Table
 *
 * Stores individual user records from imports with their canonical roles.
 * Links to user sets for versioning and rollback.
 */
export const importedUsers = pgTable("imported_users", {
  id: text("id").primaryKey(),                           // UUID for user record
  userSetId: text("user_set_id").notNull(),              // Reference to user set
  email: text("email").notNull(),                        // User email (unique within set)
  firstName: text("first_name").notNull(),               // First name
  lastName: text("last_name").notNull(),                 // Last name
  erpUserId: text("erp_user_id"),                        // Original ERP user ID
  erpRoleCodes: text("erp_role_codes").notNull(),        // JSON array of ERP role codes
  proofpayRoles: text("proofpay_roles").notNull(),       // JSON array of canonical roles
  customerFilters: text("customer_filters").notNull(),   // JSON array of customer filters
  legalEntityScope: text("legal_entity_scope"),          // JSON array of legal entities
  costCenter: text("cost_center"),                       // Cost center code
  subsidiary: text("subsidiary"),                        // Subsidiary code
  status: text("status").notNull().default("ACTIVE"),   // ACTIVE, LOCKED, PENDING
  attributes: text("attributes"),                        // JSON object for additional attributes
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

/**
 * User Import History Table
 *
 * Tracks all user import operations for audit and debugging.
 * Stores metadata about each import attempt.
 */
export const userImportHistory = pgTable("user_import_history", {
  id: text("id").primaryKey(),                           // UUID for import record
  userSetId: text("user_set_id").notNull(),              // Reference to created user set
  vendor: text("vendor"),                                // ERP vendor
  importType: text("import_type").notNull(),             // FILE, API_PULL, MANUAL
  fileName: text("file_name"),                           // Original file name if file import
  fileSize: integer("file_size"),                        // File size in bytes
  recordsProcessed: integer("records_processed").notNull().default(0),
  recordsSuccessful: integer("records_successful").notNull().default(0),
  recordsSkipped: integer("records_skipped").notNull().default(0),
  recordsErrored: integer("records_errored").notNull().default(0),
  validationErrors: text("validation_errors"),           // JSON array of validation errors
  mappingId: text("mapping_id"),                         // Reference to field mapping used
  processingTimeMs: integer("processing_time_ms"),       // Processing time in milliseconds
  status: text("status").notNull().default("PROCESSING"), // PROCESSING, COMPLETED, FAILED
  errorMessage: text("error_message"),                   // Error message if failed
  createdAt: timestamp("created_at").defaultNow().notNull(),
  completedAt: timestamp("completed_at"),                // When import completed
  createdBy: text("created_by").notNull(),               // User who initiated import
});

/**
 * User Field Mappings Table
 *
 * Stores mappings between ERP user fields and canonical user schema.
 * Extends the existing field mappings concept for user data.
 */
export const userFieldMappings = pgTable("user_field_mappings", {
  id: text("id").primaryKey(),                           // UUID for mapping
  name: text("name").notNull(),                          // Human-readable name
  vendor: text("vendor").notNull(),                      // SAP, Oracle, Dynamics365, NetSuite
  mappings: text("mappings").notNull(),                  // JSON object: vendor_field -> canonical_field
  roleTransformations: text("role_transformations"),     // JSON object: erp_role -> proofpay_roles
  defaultAttributes: text("default_attributes"),         // JSON object: default values
  validationRules: text("validation_rules"),             // JSON object: validation rules
  isDefault: boolean("is_default").notNull().default(false), // Whether this is the default mapping for vendor
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: text("created_by").notNull(),               // User who created this mapping
});

// Schema for creating user sets
export const insertUserSetSchema = createInsertSchema(userSets).omit({
  createdAt: true,
  activatedAt: true,
});

// Schema for creating imported users
export const insertImportedUserSchema = createInsertSchema(importedUsers).omit({
  createdAt: true,
  updatedAt: true,
});

// Schema for creating user import history
export const insertUserImportHistorySchema = createInsertSchema(userImportHistory).omit({
  createdAt: true,
  completedAt: true,
});

// Schema for creating user field mappings
export const insertUserFieldMappingSchema = createInsertSchema(userFieldMappings).omit({
  createdAt: true,
  updatedAt: true,
});
