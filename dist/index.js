var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";

// shared/schema.ts
var schema_exports = {};
__export(schema_exports, {
  documentApprovals: () => documentApprovals,
  fieldMappings: () => fieldMappings,
  fileFormats: () => fileFormats,
  importConnectors: () => importConnectors,
  importedUsers: () => importedUsers,
  insertFieldMappingSchema: () => insertFieldMappingSchema,
  insertImportConnectorSchema: () => insertImportConnectorSchema,
  insertImportedUserSchema: () => insertImportedUserSchema,
  insertInvoiceSchema: () => insertInvoiceSchema,
  insertPaymentSchema: () => insertPaymentSchema,
  insertReceivedPaymentSchema: () => insertReceivedPaymentSchema,
  insertRemittanceSchema: () => insertRemittanceSchema,
  insertUserFieldMappingSchema: () => insertUserFieldMappingSchema,
  insertUserImportHistorySchema: () => insertUserImportHistorySchema,
  insertUserSchema: () => insertUserSchema,
  insertUserSetSchema: () => insertUserSetSchema,
  insertWorkflowDefinitionSchema: () => insertWorkflowDefinitionSchema,
  invoices: () => invoices,
  payments: () => payments,
  receivedPayments: () => receivedPayments,
  remittances: () => remittances,
  userFieldMappings: () => userFieldMappings,
  userImportHistory: () => userImportHistory,
  userSets: () => userSets,
  users: () => users,
  workflowDefinitions: () => workflowDefinitions,
  workflowEvaluations: () => workflowEvaluations,
  workflowVersions: () => workflowVersions
});
import { pgTable, text, serial, integer, boolean, timestamp, doublePrecision } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
var users = pgTable("users", {
  id: serial("id").primaryKey(),
  // Unique user identifier
  username: text("username").notNull().unique(),
  // Unique username for login
  password: text("password").notNull()
  // Password (should be stored as a hash in production)
});
var insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true
});
var payments = pgTable("payments", {
  id: serial("id").primaryKey(),
  // Unique payment identifier
  reference: text("reference").notNull(),
  // Reference number (must be unique)
  amount: doublePrecision("amount").notNull(),
  // Payment amount
  sender: text("sender").notNull(),
  // Company sending the payment
  recipient: text("recipient").notNull(),
  // Payment recipient
  recipient_address: text("recipient_address"),
  // Optional shipping/billing address
  recipient_account: text("recipient_account"),
  // Optional account number
  status: text("status").notNull().default("Not Approved"),
  // Payment status (Not Approved, Approved, Paid, Reconciled)
  file_type: text("file_type").notNull(),
  // Format of import file (PEXR2002, MT103, ISO20022)
  approved: boolean("approved").notNull().default(false),
  // Flag for approval status
  approved_at: timestamp("approved_at"),
  // When payment was approved
  sent_at: timestamp("sent_at"),
  // When payment was sent to blockchain
  file_content: text("file_content").notNull(),
  // Original imported file content
  remittance_generated: boolean("remittance_generated").notNull().default(false),
  // Whether reconciliation file was generated
  remittance_generated_at: timestamp("remittance_generated_at"),
  // When reconciliation was generated
  remittance_id: integer("remittance_id"),
  // ID of linked reconciliation record
  signature: text("signature"),
  // BLS signature for the payment
  message: text("message"),
  // Message hash that was signed
  receipt_imported: boolean("receipt_imported").notNull().default(false),
  // Flag for tracking manual receipt imports; used to control when payments appear in reconciliation column
  created_at: timestamp("created_at").defaultNow().notNull()
  // Creation timestamp
});
var insertPaymentSchema = createInsertSchema(payments).omit({
  id: true,
  // Auto-generated primary key
  status: true,
  // System-managed status
  approved: true,
  // System-managed approval flag
  sent_at: true,
  // System-managed timestamp
  remittance_generated: true,
  // System-managed flag
  remittance_id: true,
  // System-managed relation
  created_at: true,
  // Auto-generated timestamp
  receipt_imported: true
  // System-managed flag for tracking receipt imports
});
var remittances = pgTable("remittances", {
  id: serial("id").primaryKey(),
  // Unique reconciliation identifier
  payment_id: integer("payment_id").notNull(),
  // ID of related payment or invoice
  status: text("status").notNull().default("Generated"),
  // Reconciliation status
  format: text("format").notNull(),
  // File format (MT940, BAI2, ISO20022)
  created_at: timestamp("created_at").defaultNow().notNull(),
  // Creation timestamp
  file_path: text("file_path").notNull(),
  // Location of generated file
  amount: doublePrecision("amount").notNull(),
  // Transaction amount
  sender: text("sender").notNull(),
  // Payment sender
  recipient: text("recipient").notNull(),
  // Payment recipient
  reference: text("reference").notNull()
  // Original payment reference
});
var insertRemittanceSchema = createInsertSchema(remittances).omit({
  id: true,
  // Auto-generated primary key
  status: true,
  // System-managed status
  created_at: true
  // Auto-generated timestamp
});
var invoices = pgTable("invoices", {
  id: serial("id").primaryKey(),
  // Unique invoice identifier
  customer: text("customer").notNull(),
  // Customer/client name
  amount: doublePrecision("amount").notNull(),
  // Invoice amount
  reference: text("reference").notNull(),
  // Invoice reference number
  description: text("description").notNull(),
  // Invoice description/details
  due_date: timestamp("due_date").notNull(),
  // Invoice payment due date
  status: text("status").notNull().default("Open"),
  // Invoice status (Open, Overdue, Paid, Reconciled)
  file_type: text("file_type"),
  // Format of import file (if imported)
  file_content: text("file_content"),
  // Original imported file content (if imported)
  created_at: timestamp("created_at").defaultNow().notNull(),
  // Creation timestamp
  payment_id: integer("payment_id"),
  // ID of linked received payment (if linked)
  remittance_generated: boolean("remittance_generated").notNull().default(false),
  // Whether reconciliation was generated
  remittance_generated_at: timestamp("remittance_generated_at"),
  // When reconciliation was generated
  remittance_id: integer("remittance_id")
  // ID of linked reconciliation record
});
var insertInvoiceSchema = createInsertSchema(invoices).omit({
  id: true,
  // Auto-generated primary key
  status: true,
  // System-managed status
  created_at: true,
  // Auto-generated timestamp
  payment_id: true,
  // System-managed relation
  remittance_id: true
  // System-managed relation
});
var receivedPayments = pgTable("received_payments", {
  id: serial("id").primaryKey(),
  // Unique received payment identifier
  sender: text("sender").notNull(),
  // Payment sender name
  amount: doublePrecision("amount").notNull(),
  // Payment amount
  reference: text("reference").notNull(),
  // Payment reference number
  created_at: timestamp("created_at").defaultNow().notNull(),
  // Receipt timestamp
  invoice_id: integer("invoice_id"),
  // ID of linked invoice (if linked)
  status: text("status").notNull().default("Received"),
  // Payment status (Unlinked, Linked, Reconciled)
  recipient: text("recipient").notNull().default("Your Company"),
  // Payment recipient (your company)
  remittance_generated: boolean("remittance_generated").notNull().default(false),
  // Whether reconciliation was generated
  remittance_generated_at: timestamp("remittance_generated_at"),
  // When reconciliation was generated
  remittance_id: integer("remittance_id")
  // ID of linked reconciliation record
});
var insertReceivedPaymentSchema = createInsertSchema(receivedPayments).omit({
  id: true,
  // Auto-generated primary key
  created_at: true,
  // Auto-generated timestamp
  status: true,
  // System-managed status
  remittance_generated: true,
  // System-managed flag
  remittance_id: true
  // System-managed relation
});
var fileFormats = {
  payment: ["PEXR2002", "MT103", "ISO20022"],
  // Supported payment file formats
  invoice: ["EDI X12", "ISO20022"],
  // Supported invoice file formats
  remittance: ["MT940", "BAI2", "ISO20022"]
  // Supported reconciliation file formats
};
var workflowDefinitions = pgTable("workflow_definitions", {
  id: text("id").primaryKey(),
  // UUID for workflow
  name: text("name").notNull(),
  // Human-readable name
  description: text("description"),
  // Optional description
  version: text("version").notNull(),
  // Semantic version (e.g., "1.0.0")
  documentType: text("document_type").notNull(),
  // INVOICE, PAYMENT, etc.
  status: text("status").notNull().default("ACTIVE"),
  // ACTIVE, INACTIVE, DRAFT
  definition: text("definition").notNull(),
  // JSON serialized WorkflowDefinition
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: text("created_by").notNull(),
  // User ID who created
  sourceVendor: text("source_vendor"),
  // SAP, Oracle, etc. (if imported)
  sourceImportedAt: timestamp("source_imported_at"),
  // When imported from ERP
  sourceMappingId: text("source_mapping_id")
  // Reference to field mapping used
});
var fieldMappings = pgTable("field_mappings", {
  id: text("id").primaryKey(),
  // UUID for mapping
  name: text("name").notNull(),
  // Human-readable name
  vendor: text("vendor").notNull(),
  // SAP, Oracle, Dynamics365, NetSuite
  mappings: text("mappings").notNull(),
  // JSON object: vendor_field -> canonical_field
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});
var importConnectors = pgTable("import_connectors", {
  id: text("id").primaryKey(),
  // UUID for connector
  name: text("name").notNull(),
  // Human-readable name
  vendor: text("vendor").notNull(),
  // ERP vendor
  type: text("type").notNull(),
  // API or FILE
  config: text("config").notNull(),
  // JSON configuration
  lastSync: timestamp("last_sync"),
  // Last successful sync
  status: text("status").notNull().default("ACTIVE"),
  // ACTIVE, ERROR, DISABLED
  errorMessage: text("error_message"),
  // Last error if any
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});
var workflowVersions = pgTable("workflow_versions", {
  id: text("id").primaryKey(),
  // UUID for version record
  workflowId: text("workflow_id").notNull(),
  // Reference to workflow
  version: text("version").notNull(),
  // Version string
  definition: text("definition").notNull(),
  // Full workflow definition JSON
  changeLog: text("change_log"),
  // Optional change description
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: text("created_by").notNull()
  // User who created this version
});
var workflowEvaluations = pgTable("workflow_evaluations", {
  id: text("id").primaryKey(),
  // UUID for evaluation
  workflowId: text("workflow_id").notNull(),
  // Which workflow was evaluated
  documentId: text("document_id").notNull(),
  // Document that was evaluated
  documentType: text("document_type").notNull(),
  // Type of document
  result: text("result").notNull(),
  // JSON serialized EvaluationResult
  evaluationTimeMs: integer("evaluation_time_ms").notNull(),
  // Performance metric
  evaluatedAt: timestamp("evaluated_at").defaultNow().notNull(),
  evaluatedBy: text("evaluated_by")
  // User or system that triggered
});
var documentApprovals = pgTable("document_approvals", {
  id: text("id").primaryKey(),
  // UUID for approval record
  documentId: text("document_id").notNull(),
  // Reference to payment/invoice
  documentType: text("document_type").notNull(),
  // INVOICE, PAYMENT, etc.
  workflowId: text("workflow_id").notNull(),
  // Which workflow is being followed
  stepId: text("step_id").notNull(),
  // Current workflow step
  stepSequence: integer("step_sequence").notNull(),
  // Step order number
  status: text("status").notNull().default("PENDING"),
  // PENDING, APPROVED, REJECTED, TIMEOUT
  approverUserId: text("approver_user_id"),
  // Who approved/rejected
  approvedAt: timestamp("approved_at"),
  // When approved/rejected
  comments: text("comments"),
  // Optional approval comments
  timeoutAt: timestamp("timeout_at"),
  // When this step times out
  createdAt: timestamp("created_at").defaultNow().notNull()
});
var insertWorkflowDefinitionSchema = createInsertSchema(workflowDefinitions).omit({
  createdAt: true,
  updatedAt: true
});
var insertFieldMappingSchema = createInsertSchema(fieldMappings).omit({
  createdAt: true,
  updatedAt: true
});
var insertImportConnectorSchema = createInsertSchema(importConnectors).omit({
  createdAt: true,
  updatedAt: true,
  lastSync: true,
  errorMessage: true
});
var userSets = pgTable("user_sets", {
  id: text("id").primaryKey(),
  // UUID for user set
  version: text("version").notNull(),
  // Version string (e.g., "1.0.0")
  name: text("name").notNull(),
  // Human-readable name
  description: text("description"),
  // Optional description
  vendor: text("vendor"),
  // ERP vendor if imported
  status: text("status").notNull().default("DRAFT"),
  // DRAFT, ACTIVE, ARCHIVED
  userCount: integer("user_count").notNull().default(0),
  // Number of users in this set
  changeLog: text("change_log"),
  // Summary of changes
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdBy: text("created_by").notNull(),
  // User who created this set
  activatedAt: timestamp("activated_at")
  // When this set became active
});
var importedUsers = pgTable("imported_users", {
  id: text("id").primaryKey(),
  // UUID for user record
  userSetId: text("user_set_id").notNull(),
  // Reference to user set
  email: text("email").notNull(),
  // User email (unique within set)
  firstName: text("first_name").notNull(),
  // First name
  lastName: text("last_name").notNull(),
  // Last name
  erpUserId: text("erp_user_id"),
  // Original ERP user ID
  erpRoleCodes: text("erp_role_codes").notNull(),
  // JSON array of ERP role codes
  proofpayRoles: text("proofpay_roles").notNull(),
  // JSON array of canonical roles
  customerFilters: text("customer_filters").notNull(),
  // JSON array of customer filters
  legalEntityScope: text("legal_entity_scope"),
  // JSON array of legal entities
  costCenter: text("cost_center"),
  // Cost center code
  subsidiary: text("subsidiary"),
  // Subsidiary code
  status: text("status").notNull().default("ACTIVE"),
  // ACTIVE, LOCKED, PENDING
  attributes: text("attributes"),
  // JSON object for additional attributes
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull()
});
var userImportHistory = pgTable("user_import_history", {
  id: text("id").primaryKey(),
  // UUID for import record
  userSetId: text("user_set_id").notNull(),
  // Reference to created user set
  vendor: text("vendor"),
  // ERP vendor
  importType: text("import_type").notNull(),
  // FILE, API_PULL, MANUAL
  fileName: text("file_name"),
  // Original file name if file import
  fileSize: integer("file_size"),
  // File size in bytes
  recordsProcessed: integer("records_processed").notNull().default(0),
  recordsSuccessful: integer("records_successful").notNull().default(0),
  recordsSkipped: integer("records_skipped").notNull().default(0),
  recordsErrored: integer("records_errored").notNull().default(0),
  validationErrors: text("validation_errors"),
  // JSON array of validation errors
  mappingId: text("mapping_id"),
  // Reference to field mapping used
  processingTimeMs: integer("processing_time_ms"),
  // Processing time in milliseconds
  status: text("status").notNull().default("PROCESSING"),
  // PROCESSING, COMPLETED, FAILED
  errorMessage: text("error_message"),
  // Error message if failed
  createdAt: timestamp("created_at").defaultNow().notNull(),
  completedAt: timestamp("completed_at"),
  // When import completed
  createdBy: text("created_by").notNull()
  // User who initiated import
});
var userFieldMappings = pgTable("user_field_mappings", {
  id: text("id").primaryKey(),
  // UUID for mapping
  name: text("name").notNull(),
  // Human-readable name
  vendor: text("vendor").notNull(),
  // SAP, Oracle, Dynamics365, NetSuite
  mappings: text("mappings").notNull(),
  // JSON object: vendor_field -> canonical_field
  roleTransformations: text("role_transformations"),
  // JSON object: erp_role -> proofpay_roles
  defaultAttributes: text("default_attributes"),
  // JSON object: default values
  validationRules: text("validation_rules"),
  // JSON object: validation rules
  isDefault: boolean("is_default").notNull().default(false),
  // Whether this is the default mapping for vendor
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  createdBy: text("created_by").notNull()
  // User who created this mapping
});
var insertUserSetSchema = createInsertSchema(userSets).omit({
  createdAt: true,
  activatedAt: true
});
var insertImportedUserSchema = createInsertSchema(importedUsers).omit({
  createdAt: true,
  updatedAt: true
});
var insertUserImportHistorySchema = createInsertSchema(userImportHistory).omit({
  createdAt: true,
  completedAt: true
});
var insertUserFieldMappingSchema = createInsertSchema(userFieldMappings).omit({
  createdAt: true,
  updatedAt: true
});

// server/db.ts
import pkg from "pg";
import { drizzle } from "drizzle-orm/node-postgres";
import path from "path";
import fs from "fs";
import dotenv from "dotenv";
import process2 from "process";
var { Pool } = pkg;
var dotenvPath = path.resolve(process2.cwd(), ".env");
if (fs.existsSync(dotenvPath)) {
  dotenv.config({ path: dotenvPath });
  console.log("Loaded environment variables from .env file");
}
if (!process2.env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL must be set. Did you forget to provision a database or create a .env file?"
  );
}
var pool;
var db;
if (process2.env.DATABASE_URL.includes("neon.tech")) {
  import("@neondatabase/serverless").then(({ Pool: NeonPool, neonConfig }) => {
    import("ws").then((ws) => {
      neonConfig.webSocketConstructor = ws.default;
      pool = new NeonPool({ connectionString: process2.env.DATABASE_URL });
      db = drizzle(pool, { schema: schema_exports });
      console.log("Connected to Neon PostgreSQL database");
    });
  }).catch((err) => {
    console.error("Failed to load Neon PostgreSQL driver:", err);
    process2.exit(1);
  });
} else {
  pool = new Pool({ connectionString: process2.env.DATABASE_URL });
  db = drizzle(pool, { schema: schema_exports });
  console.log("Connected to standard PostgreSQL database");
}

// server/storage.ts
import { eq } from "drizzle-orm";
import fs2 from "fs";
import path2 from "path";
var DatabaseStorage = class {
  filesDir;
  constructor() {
    this.filesDir = path2.join(process.cwd(), "files");
    if (!fs2.existsSync(this.filesDir)) {
      fs2.mkdirSync(this.filesDir, { recursive: true });
    }
  }
  // User methods
  async getUser(id) {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }
  async getUserByUsername(username) {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }
  async createUser(user) {
    const [createdUser] = await db.insert(users).values(user).returning();
    return createdUser;
  }
  // Payment methods
  async getPaymentByReference(reference) {
    const [payment] = await db.select().from(payments).where(eq(payments.reference, reference));
    return payment;
  }
  async createPayment(payment) {
    const [createdPayment] = await db.insert(payments).values(payment).returning();
    return createdPayment;
  }
  async getPayment(id) {
    const [payment] = await db.select().from(payments).where(eq(payments.id, id));
    return payment;
  }
  async getAllPayments() {
    return await db.select().from(payments);
  }
  async getPaymentsByStatus(status) {
    return await db.select().from(payments).where(eq(payments.status, status));
  }
  async approvePayment(id, signature, message) {
    const timestamp2 = /* @__PURE__ */ new Date();
    const [updatedPayment] = await db.update(payments).set({
      approved: true,
      status: "Approved",
      approved_at: timestamp2,
      signature: signature || null,
      message: message || null
    }).where(eq(payments.id, id)).returning();
    return updatedPayment;
  }
  async revokePaymentApproval(id) {
    const [updatedPayment] = await db.update(payments).set({
      approved: false,
      status: "Not Approved"
    }).where(eq(payments.id, id)).returning();
    return updatedPayment;
  }
  async sendPayment(id) {
    const [updatedPayment] = await db.update(payments).set({
      sent_at: /* @__PURE__ */ new Date(),
      status: "Paid"
      // Ensuring we use "Paid" status consistently
    }).where(eq(payments.id, id)).returning();
    return updatedPayment;
  }
  async updatePaymentRemittanceStatus(id, remittanceId) {
    const timestamp2 = /* @__PURE__ */ new Date();
    const [updatedPayment] = await db.update(payments).set({
      remittance_generated: true,
      remittance_generated_at: timestamp2,
      remittance_id: remittanceId,
      status: "Remitted"
    }).where(eq(payments.id, id)).returning();
    return updatedPayment;
  }
  // Remittance methods
  async createRemittance(remittance) {
    const [createdRemittance] = await db.insert(remittances).values(remittance).returning();
    return createdRemittance;
  }
  async getRemittance(id) {
    const [remittance] = await db.select().from(remittances).where(eq(remittances.id, id));
    return remittance;
  }
  async getAllRemittances() {
    return await db.select().from(remittances);
  }
  async getRemittancesByPaymentId(paymentId) {
    return await db.select().from(remittances).where(eq(remittances.payment_id, paymentId));
  }
  // Invoice methods
  async getInvoiceByReference(reference) {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.reference, reference));
    return invoice;
  }
  async createInvoice(invoice) {
    const [createdInvoice] = await db.insert(invoices).values(invoice).returning();
    return createdInvoice;
  }
  async getInvoice(id) {
    const [invoice] = await db.select().from(invoices).where(eq(invoices.id, id));
    return invoice;
  }
  async getAllInvoices() {
    return await db.select().from(invoices);
  }
  async getInvoicesByStatus(status) {
    return await db.select().from(invoices).where(eq(invoices.status, status));
  }
  async updateInvoiceStatus(id, status) {
    const [updatedInvoice] = await db.update(invoices).set({ status }).where(eq(invoices.id, id)).returning();
    return updatedInvoice;
  }
  async linkInvoiceToPayment(id, paymentId) {
    const [updatedInvoice] = await db.update(invoices).set({
      payment_id: paymentId,
      status: "Paid"
    }).where(eq(invoices.id, id)).returning();
    return updatedInvoice;
  }
  // Received Payment methods
  async createReceivedPayment(receivedPayment) {
    const receivedPaymentWithStatus = {
      ...receivedPayment,
      status: "Unlinked"
    };
    const [createdPayment] = await db.insert(receivedPayments).values(receivedPaymentWithStatus).returning();
    if (createdPayment.reference) {
      const [matchingInvoice] = await db.select().from(invoices).where(eq(invoices.reference, createdPayment.reference));
      if (matchingInvoice) {
        const [updatedPayment] = await db.update(receivedPayments).set({
          invoice_id: matchingInvoice.id,
          status: "Linked"
        }).where(eq(receivedPayments.id, createdPayment.id)).returning();
        await db.update(invoices).set({
          status: "Paid",
          payment_id: createdPayment.id
        }).where(eq(invoices.id, matchingInvoice.id));
        return updatedPayment;
      }
    }
    return createdPayment;
  }
  async getReceivedPayment(id) {
    const [receivedPayment] = await db.select({
      id: receivedPayments.id,
      sender: receivedPayments.sender,
      amount: receivedPayments.amount,
      reference: receivedPayments.reference,
      created_at: receivedPayments.created_at,
      invoice_id: receivedPayments.invoice_id,
      status: receivedPayments.status,
      remittance_generated: receivedPayments.remittance_generated,
      remittance_generated_at: receivedPayments.remittance_generated_at,
      remittance_id: receivedPayments.remittance_id
    }).from(receivedPayments).where(eq(receivedPayments.id, id));
    return receivedPayment;
  }
  async getAllReceivedPayments() {
    return await db.select({
      id: receivedPayments.id,
      sender: receivedPayments.sender,
      amount: receivedPayments.amount,
      reference: receivedPayments.reference,
      created_at: receivedPayments.created_at,
      invoice_id: receivedPayments.invoice_id,
      status: receivedPayments.status,
      remittance_generated: receivedPayments.remittance_generated,
      remittance_generated_at: receivedPayments.remittance_generated_at,
      remittance_id: receivedPayments.remittance_id
    }).from(receivedPayments);
  }
  async getReceivedPaymentsByStatus(status) {
    return await db.select({
      id: receivedPayments.id,
      sender: receivedPayments.sender,
      amount: receivedPayments.amount,
      reference: receivedPayments.reference,
      created_at: receivedPayments.created_at,
      invoice_id: receivedPayments.invoice_id,
      status: receivedPayments.status,
      remittance_generated: receivedPayments.remittance_generated,
      remittance_generated_at: receivedPayments.remittance_generated_at,
      remittance_id: receivedPayments.remittance_id
    }).from(receivedPayments).where(eq(receivedPayments.status, status));
  }
  async linkReceivedPaymentToInvoice(id, invoiceId) {
    console.log(`Linking received payment ${id} to invoice ${invoiceId}`);
    const [updatedPayment] = await db.update(receivedPayments).set({
      invoice_id: invoiceId,
      status: "Linked"
    }).where(eq(receivedPayments.id, id)).returning({
      id: receivedPayments.id,
      sender: receivedPayments.sender,
      amount: receivedPayments.amount,
      reference: receivedPayments.reference,
      created_at: receivedPayments.created_at,
      invoice_id: receivedPayments.invoice_id,
      status: receivedPayments.status,
      remittance_generated: receivedPayments.remittance_generated,
      remittance_generated_at: receivedPayments.remittance_generated_at,
      remittance_id: receivedPayments.remittance_id
    });
    console.log(`Setting invoice ${invoiceId} status to Paid and payment_id to ${id}`);
    const [updatedInvoice] = await db.update(invoices).set({
      status: "Paid",
      payment_id: id
    }).where(eq(invoices.id, invoiceId)).returning();
    console.log(`Invoice updated:`, updatedInvoice ? `Status: ${updatedInvoice.status}, Payment ID: ${updatedInvoice.payment_id}` : "No invoice returned");
    return updatedPayment;
  }
  async updateReceivedPaymentRemittanceStatus(id, remittanceId) {
    const timestamp2 = /* @__PURE__ */ new Date();
    const [updatedPayment] = await db.update(receivedPayments).set({
      remittance_generated: true,
      remittance_generated_at: timestamp2,
      remittance_id: remittanceId,
      status: "Remitted"
    }).where(eq(receivedPayments.id, id)).returning();
    if (updatedPayment.invoice_id) {
      await db.update(invoices).set({
        status: "Remitted",
        remittance_id: remittanceId
      }).where(eq(invoices.id, updatedPayment.invoice_id));
    }
    return updatedPayment;
  }
  // File storage methods
  async saveFile(content, type, format) {
    const timestamp2 = Date.now();
    const filename = `${type}_${format}_${timestamp2}.txt`;
    const filePath = path2.join(this.filesDir, filename);
    await fs2.promises.writeFile(filePath, content, "utf8");
    return filename;
  }
  async getFileContent(filePath) {
    const fullPath = path2.join(this.filesDir, filePath);
    return await fs2.promises.readFile(fullPath, "utf8");
  }
  // Manual import methods
  /**
   * Import a received payment manually
   * 
   * This method creates a new received payment record based on imported JSON data.
   * It supports the Accounts Receivable workflow by allowing manual import of
   * payment data that would normally come from external systems.
   * 
   * The method accommodates both "sender" and "from" field names for flexibility,
   * allowing users to import payments in various formats.
   * 
   * @param data - Object containing sender name, amount, and reference number
   * @returns The created received payment record
   */
  async markPaymentReceived(data) {
    const reference = data.reference;
    const receivedPaymentData = {
      reference,
      amount: data.amount,
      sender: data.from,
      recipient: "Your Company",
      invoice_id: null
    };
    const receivedPayment = await this.createReceivedPayment(receivedPaymentData);
    return receivedPayment;
  }
  /**
   * Mark a payment as having its receipt imported
   * 
   * This method updates a payment record to indicate a receipt has been imported
   * without changing its status from "Paid" to "Reconciled". This allows payments 
   * to appear in the reconciliation column for manual verification while maintaining
   * their Paid status.
   * 
   * The approach fixes the workflow so that:
   * 1. Sent payments don't automatically populate the reconciliation column
   * 2. Payments with imported receipts show in the reconciliation column
   * 3. Status remains "Paid" until reconciliation is manually generated
   * 
   * @param data - Object containing account ID, amount, and reference number
   * @returns The updated payment record or undefined if not found
   */
  async markReceiptImported(data) {
    const payment = await this.getPaymentByReference(data.reference);
    if (!payment) {
      return void 0;
    }
    const [updatedPayment] = await db.update(payments).set({
      receipt_imported: true
      // Add a flag to indicate receipt was imported
    }).where(eq(payments.reference, data.reference)).returning();
    return updatedPayment;
  }
};
var storage = new DatabaseStorage();

// server/routes.ts
import { z } from "zod";

// server/utils/fileProcessors.ts
function detectFileFormat(content) {
  const normalizedContent = content.trim();
  const lowerContent = normalizedContent.toLowerCase();
  console.log("Detecting file format...");
  let invoiceScore = 0;
  let paymentScore = 0;
  const invoiceKeywords = ["invoice", "bill", "statement", "due date", "customer invoice", "billing"];
  for (const keyword of invoiceKeywords) {
    if (lowerContent.includes(keyword)) {
      invoiceScore += 2;
      console.log(`Found invoice keyword: ${keyword}`);
    }
  }
  const paymentKeywords = ["payment", "remittance", "transfer", "transaction", "wire"];
  for (const keyword of paymentKeywords) {
    if (lowerContent.includes(keyword)) {
      paymentScore += 2;
      console.log(`Found payment keyword: ${keyword}`);
    }
  }
  if (normalizedContent.includes("<?xml") && (normalizedContent.includes("CstmrInvcData") || normalizedContent.includes("<Invc>") || normalizedContent.includes("<InvcId>") || normalizedContent.includes("<TtlInvcAmt>"))) {
    console.log("Detected ISO20022 invoice format");
    return { type: "invoice", format: "ISO20022" };
  }
  if (normalizedContent.includes("ISA*") && normalizedContent.includes("GS*") && normalizedContent.includes("ST*810") || normalizedContent.includes("ISA*") && normalizedContent.includes("*810*")) {
    console.log("Detected EDI X12 invoice format");
    return { type: "invoice", format: "EDI X12" };
  }
  if (normalizedContent.includes("PEXR2002") || normalizedContent.includes("HDR:") && normalizedContent.includes("REF:")) {
    console.log("Detected PEXR2002 payment format");
    return { type: "payment", format: "PEXR2002" };
  }
  if (normalizedContent.includes("{1:F01") && normalizedContent.includes(":20:") && normalizedContent.includes(":32A:")) {
    console.log("Detected MT103 payment format");
    return { type: "payment", format: "MT103" };
  }
  if (normalizedContent.includes("<?xml") && (normalizedContent.includes('xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001') || normalizedContent.includes("<CstmrCdtTrfInitn>"))) {
    console.log("Detected ISO20022 payment format");
    return { type: "payment", format: "ISO20022" };
  }
  if (normalizedContent.includes("ISA*") || normalizedContent.includes("GS*")) {
    if (normalizedContent.includes("BIG*") || normalizedContent.includes("ITD*") || normalizedContent.includes("TDS*")) {
      console.log("Detected EDI with invoice segments, treating as EDI X12 invoice");
      return { type: "invoice", format: "EDI X12" };
    }
  }
  if (normalizedContent.includes("<?xml")) {
    const invoiceXmlElements = ["invoice", "bill", "invoiceid", "invoiceamount", "customer", "totalamount"];
    for (const elem of invoiceXmlElements) {
      if (lowerContent.includes(`<${elem}`) || lowerContent.includes(`<${elem.toLowerCase()}`)) {
        invoiceScore += 2;
        console.log(`Found invoice XML element: ${elem}`);
      }
    }
    if (invoiceScore > 3) {
      console.log(`Detected generic XML invoice content (score: ${invoiceScore})`);
      return { type: "invoice", format: "ISO20022" };
    }
  }
  const invoiceRegex = /invoice\s*#|invoice\s*no|invoice\s*number|bill\s*to|ship\s*to|customer\s*po|invoice\s*total/i;
  if (invoiceRegex.test(normalizedContent)) {
    console.log("Detected invoice field patterns, treating as ISO20022 invoice");
    return { type: "invoice", format: "ISO20022" };
  }
  if (lowerContent.match(/total\s*amount|amount\s*due|balance\s*due|outstanding\s*balance/)) {
    invoiceScore += 3;
    console.log("Found invoice amount pattern");
  }
  if (lowerContent.match(/due\s*date|payment\s*due|invoice\s*date/)) {
    invoiceScore += 2;
    console.log("Found invoice date pattern");
  }
  if (invoiceScore > paymentScore) {
    console.log(`Content appears to be an invoice (score: ${invoiceScore}>${paymentScore}), treating as ISO20022 invoice`);
    return { type: "invoice", format: "ISO20022" };
  } else if (paymentScore > invoiceScore) {
    console.log(`Content appears to be a payment (score: ${paymentScore}>${invoiceScore}), treating as PEXR2002 payment`);
    return { type: "payment", format: "PEXR2002" };
  }
  console.log("Format detection inconclusive, defaulting to ISO20022 invoice");
  return { type: "invoice", format: "ISO20022" };
}
function processPaymentFile(content, format) {
  if (!format) {
    const detected = detectFileFormat(content);
    if (detected.type !== "payment") {
      throw new Error(`Detected file is not a payment file. It appears to be a ${detected.type} file.`);
    }
    format = detected.format;
    console.log(`Auto-detected payment format: ${format}`);
  }
  switch (format) {
    case "PEXR2002":
      return parsePEXR2002(content);
    case "MT103":
      return parseMT103(content);
    case "ISO20022":
      return parseISO20022Payment(content);
    default:
      throw new Error(`Unsupported payment file format: ${format}`);
  }
}
function processInvoiceFile(content, format) {
  if (!format) {
    const detected = detectFileFormat(content);
    if (detected.type !== "invoice") {
      throw new Error(`Detected file is not an invoice file. It appears to be a ${detected.type} file.`);
    }
    format = detected.format;
    console.log(`Auto-detected invoice format: ${format}`);
  }
  switch (format) {
    case "EDI X12":
      return parseEDIX12(content);
    case "ISO20022":
      return parseISO20022Invoice(content);
    default:
      throw new Error(`Unsupported invoice file format: ${format}`);
  }
}
function generateRandomRef() {
  const now = /* @__PURE__ */ new Date();
  const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
  return `REF-${dateStr}-${Math.floor(Math.random() * 1e3).toString().padStart(3, "0")}`;
}
function formatReference(reference) {
  return reference;
}
function parsePEXR2002(content) {
  try {
    const lines = content.trim().split("\n");
    if (!lines[0] || !lines[0].includes("PEXR2002")) {
      const defaultPexr = `PEXR2002
HDR:${(/* @__PURE__ */ new Date()).toISOString().split("T")[0].replace(/-/g, "")}
${content}`;
      return parsePEXR2002(defaultPexr);
    }
    const reference = lines.find((l) => l.startsWith("REF:"))?.substring(4).trim() || generateRandomRef();
    const amountStr = lines.find((l) => l.startsWith("AMT:"))?.substring(4).trim();
    const amount = amountStr ? parseFloat(amountStr) : 1e3;
    const sender = lines.find((l) => l.startsWith("SND:"))?.substring(4).trim() || "Company Inc.";
    const recipient = lines.find((l) => l.startsWith("RCV:"))?.substring(4).trim() || "Vendor LLC";
    let recipientAddress = lines.find((l) => l.startsWith("RECEIVERIBAN:"))?.substring(13).trim();
    if (!recipientAddress) {
      recipientAddress = lines.find((l) => l.startsWith("BANKN:"))?.substring(6).trim();
    }
    return {
      reference: formatReference(reference),
      amount,
      sender,
      recipient,
      recipient_address: recipientAddress || null,
      file_type: "PEXR2002",
      file_content: content
    };
  } catch (error) {
    throw new Error(`Failed to parse PEXR2002 file: ${error instanceof Error ? error.message : String(error)}`);
  }
}
function parseMT103(content) {
  try {
    const lines = content.trim().split("\n");
    const reference = lines.find((l) => l.startsWith(":20:"))?.substring(4).trim() || "UNKNOWN";
    const amountLine = lines.find((l) => l.startsWith(":32A:"))?.substring(5).trim() || "";
    const amount = parseFloat(amountLine.substring(amountLine.length - 15).trim());
    const sender = lines.find((l) => l.startsWith(":50K:"))?.substring(5).trim() || "UNKNOWN";
    let recipient = "UNKNOWN";
    let recipientAddress = null;
    const recipientIndex = lines.findIndex((l) => l.startsWith(":59:"));
    if (recipientIndex >= 0) {
      recipientAddress = lines[recipientIndex].substring(4).trim();
      if (recipientIndex + 1 < lines.length) {
        const nextLines = lines.slice(recipientIndex + 1);
        const nameLineIndex = nextLines.findIndex((l) => !l.startsWith(":"));
        if (nameLineIndex >= 0) {
          recipient = nextLines[nameLineIndex].trim();
        }
      }
    }
    if (reference === "UNKNOWN" || isNaN(amount) || sender === "UNKNOWN" || recipient === "UNKNOWN") {
      throw new Error("Required payment fields missing in MT103 file");
    }
    return {
      reference: formatReference(reference),
      amount,
      sender,
      recipient,
      recipient_address: recipientAddress,
      file_type: "MT103",
      file_content: content
    };
  } catch (error) {
    throw new Error(`Failed to parse MT103 file: ${error instanceof Error ? error.message : String(error)}`);
  }
}
function parseISO20022Payment(content) {
  try {
    const referenceMatch = content.match(/<MsgId>(.*?)<\/MsgId>/);
    const amountMatch = content.match(/<InstdAmt.*?>(.*?)<\/InstdAmt>/);
    const senderMatch = content.match(/<Dbtr>[\s\S]*?<Nm>(.*?)<\/Nm>/);
    const recipientMatch = content.match(/<Cdtr>[\s\S]*?<Nm>(.*?)<\/Nm>/);
    const cdtrAcctSection = content.match(/<CdtrAcct>[\s\S]*?<\/CdtrAcct>/);
    let recipientAccount = null;
    if (cdtrAcctSection) {
      const creditorIbanMatch = cdtrAcctSection[0].match(/<IBAN>(.*?)<\/IBAN>/);
      if (creditorIbanMatch) {
        recipientAccount = creditorIbanMatch[1].replace(/^\/+/, "");
      }
    }
    const reference = referenceMatch ? referenceMatch[1] : "UNKNOWN";
    const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;
    const sender = senderMatch ? senderMatch[1] : "UNKNOWN";
    const recipient = recipientMatch ? recipientMatch[1] : "UNKNOWN";
    if (reference === "UNKNOWN" || amount === 0 || sender === "UNKNOWN" || recipient === "UNKNOWN") {
      throw new Error("Required payment fields missing in ISO20022 file");
    }
    return {
      reference: formatReference(reference),
      amount,
      sender,
      recipient,
      recipient_address: null,
      recipient_account: recipientAccount,
      file_type: "ISO20022",
      file_content: content
    };
  } catch (error) {
    throw new Error(`Failed to parse ISO20022 payment file: ${error instanceof Error ? error.message : String(error)}`);
  }
}
function parseEDIX12(content) {
  try {
    console.log("Processing EDI X12 invoice content:", content.substring(0, 100));
    const lines = content.split("\n").map((line) => line.trim());
    const cleanLines = lines.filter((line) => !line.startsWith("/*") && !line.startsWith("//") && line.trim() !== "");
    let reference = "UNKNOWN";
    let customer = "UNKNOWN";
    let amount = 0;
    let dueDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3);
    const refMatch = content.match(/REF-[0-9]{8}-[0-9]{3}/);
    if (refMatch) {
      reference = refMatch[0];
      console.log(`Found reference pattern: ${reference}`);
    }
    let bigLine = cleanLines.find((line) => line.startsWith("BIG*"));
    if (bigLine) {
      const bigParts = bigLine.split("*");
      if (bigParts.length > 2) {
        if (reference === "UNKNOWN") {
          reference = bigParts[2];
        }
        if (bigParts.length > 1) {
          const dateStr = bigParts[1];
          if (dateStr && dateStr.length === 8) {
            try {
              dueDate = new Date(
                parseInt(dateStr.substring(0, 4)),
                parseInt(dateStr.substring(4, 6)) - 1,
                parseInt(dateStr.substring(6, 8))
              );
            } catch (e) {
              console.error("Could not parse date:", dateStr, e);
            }
          }
        }
        console.log(`Found BIG segment with reference: ${reference}`);
      }
    }
    let customerLine = cleanLines.find((line) => line.startsWith("N1*BY") || line.startsWith("N1*ST"));
    if (customerLine) {
      const customerParts = customerLine.split("*");
      if (customerParts.length > 2) {
        customer = customerParts[2];
        console.log(`Found customer: ${customer}`);
      }
    }
    let amountLine = cleanLines.find((line) => line.startsWith("TDS*"));
    if (amountLine) {
      const amountParts = amountLine.split("*");
      if (amountParts.length > 1) {
        let amountStr = amountParts[1];
        if (amountStr.length > 2) {
          if (!amountStr.includes(".")) {
            const decimalAmount = parseFloat(amountStr) / 100;
            amount = decimalAmount;
            console.log(`Parsed amount with implied decimal: ${amountStr} -> ${amount}`);
          } else {
            amount = parseFloat(amountStr);
            console.log(`Parsed amount with explicit decimal: ${amountStr} -> ${amount}`);
          }
        } else {
          amount = parseFloat(amountParts[1]);
        }
      }
    }
    if (amount === 0) {
      const it1Lines = cleanLines.filter((line) => line.startsWith("IT1*"));
      if (it1Lines.length > 0) {
        let totalAmount = 0;
        for (const line of it1Lines) {
          const parts = line.split("*");
          if (parts.length > 4) {
            const itemQty = parseFloat(parts[2] || "0");
            const itemPrice = parseFloat(parts[3] || "0");
            totalAmount += itemQty * itemPrice;
          }
        }
        amount = totalAmount;
        console.log(`Calculated total amount from line items: ${amount}`);
      }
    }
    const description = `Invoice ${reference} from ${customer}`;
    if (reference === "UNKNOWN") {
      const invoiceMatch = content.match(/(INV|INVOICE)[^A-Za-z0-9]?([A-Za-z0-9-]+)/i);
      if (invoiceMatch) {
        reference = invoiceMatch[2];
      } else {
        const contentHash = content.split("").reduce((acc, char) => acc * 31 + char.charCodeAt(0) & 4294967295, 0);
        const now = /* @__PURE__ */ new Date();
        const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
        reference = `EDI-${dateStr}-${contentHash % 1e3}`;
      }
      console.log(`Generated reference: ${reference}`);
    }
    if (customer === "UNKNOWN") {
      const companyMatch = content.match(/([A-Z][A-Za-z]+\s+(Inc|LLC|Ltd|Corp|GmbH|SA|NV))/);
      if (companyMatch) {
        customer = companyMatch[1];
      } else {
        customer = "Digital Systems";
      }
      console.log(`Using customer: ${customer}`);
    }
    if (amount === 0) {
      const amountMatch = content.match(/\$([0-9,]+(\.[0-9]{2})?)/);
      if (amountMatch) {
        amount = parseFloat(amountMatch[1].replace(/,/g, ""));
      } else {
        amount = 1e3;
      }
      console.log(`Using amount: ${amount}`);
    }
    console.log(`Successfully parsed EDI X12 invoice: Ref=${reference}, Amount=${amount}, Customer=${customer}`);
    return {
      reference: formatReference(reference),
      customer,
      amount,
      description,
      due_date: dueDate
    };
  } catch (error) {
    console.error(`Error parsing EDI X12 invoice:`, error);
    const now = /* @__PURE__ */ new Date();
    const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
    const fallbackReference = `EDI-${dateStr}-${Math.floor(Math.random() * 1e3).toString().padStart(3, "0")}`;
    const dueDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3);
    console.log(`Using fallback invoice generation for unparseable EDI X12 file: ${fallbackReference}`);
    return {
      reference: formatReference(fallbackReference),
      customer: "Digital Systems",
      amount: 999.99,
      description: "Invoice generated from unprocessable EDI X12 file",
      due_date: dueDate
    };
  }
}
function parseISO20022Invoice(content) {
  try {
    let referenceMatch = content.match(/<InvcId>(.*?)<\/InvcId>/);
    let amountMatch = content.match(/<TtlInvcAmt.*?>(.*?)<\/TtlInvcAmt>/);
    let customerMatch = content.match(/<Dbtr>[\s\S]*?<Nm>(.*?)<\/Nm>/);
    let descriptionMatch = content.match(/<AddtlInf>(.*?)<\/AddtlInf>/);
    let dueDateMatch = content.match(/<DuePyblDt>(.*?)<\/DuePyblDt>/);
    if (!referenceMatch) {
      referenceMatch = content.match(/<Id>(.*?)<\/Id>/) || content.match(/<RefNb>(.*?)<\/RefNb>/) || content.match(/<DocId>(.*?)<\/DocId>/) || content.match(/<MsgId>(.*?)<\/MsgId>/);
    }
    if (!amountMatch) {
      amountMatch = content.match(/<Amt.*?>(.*?)<\/Amt>/) || content.match(/<InstdAmt.*?>(.*?)<\/InstdAmt>/) || content.match(/<IntrBkSttlmAmt.*?>(.*?)<\/IntrBkSttlmAmt>/);
    }
    if (!customerMatch) {
      customerMatch = content.match(/<Cdtr>[\s\S]*?<Nm>(.*?)<\/Nm>/) || content.match(/<Pty>[\s\S]*?<Nm>(.*?)<\/Nm>/) || content.match(/<CstmrNm>(.*?)<\/CstmrNm>/);
    }
    const now = /* @__PURE__ */ new Date();
    const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
    let reference = referenceMatch ? referenceMatch[1] : null;
    if (!reference || reference === "UNKNOWN") {
      const contentHash = content.split("").reduce((acc, char) => acc * 31 + char.charCodeAt(0) & 4294967295, 0);
      reference = `INV-${dateStr}-${contentHash % 1e3}`;
      console.log(`Generated reference: ${reference} for ISO20022 file without reference tag`);
    }
    let amount = 0;
    if (amountMatch) {
      const cleanAmount = amountMatch[1].replace(/[^\d.]/g, "");
      amount = parseFloat(cleanAmount);
    }
    if (isNaN(amount) || amount === 0) {
      amount = Math.floor(Math.random() * 9900 + 100) / 100;
      console.log(`Using fallback amount: ${amount} for ISO20022 file without amount tag`);
    }
    const customer = customerMatch ? customerMatch[1] : "Tech Solutions Inc";
    const description = descriptionMatch ? descriptionMatch[1] : "Professional services rendered";
    let dueDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3);
    if (dueDateMatch) {
      const dateStr2 = dueDateMatch[1].trim();
      const dateParts = dateStr2.split("-");
      if (dateParts.length === 3) {
        dueDate = new Date(
          parseInt(dateParts[0]),
          parseInt(dateParts[1]) - 1,
          parseInt(dateParts[2])
        );
      }
    }
    console.log(`Successfully parsed ISO20022 invoice: Ref=${reference}, Amount=${amount}, Customer=${customer}`);
    return {
      reference: formatReference(reference),
      customer,
      amount,
      description,
      due_date: dueDate
    };
  } catch (error) {
    console.error(`Error parsing ISO20022 invoice:`, error);
    const now = /* @__PURE__ */ new Date();
    const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
    const fallbackReference = `INV-${dateStr}-${Math.floor(Math.random() * 1e3).toString().padStart(3, "0")}`;
    const dueDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3);
    console.log(`Using fallback invoice generation for unparseable ISO20022 file: ${fallbackReference}`);
    return {
      reference: formatReference(fallbackReference),
      customer: "Tech Solutions Inc",
      amount: 999.99,
      description: "Invoice generated from unprocessable file",
      due_date: dueDate
    };
  }
}
function generateSamplePaymentFile(format) {
  const now = /* @__PURE__ */ new Date();
  const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
  const reference = `INV-${dateStr}-${Math.floor(Math.random() * 1e3).toString().padStart(3, "0")}`;
  const amount = Math.floor(Math.random() * 1e7) / 100;
  switch (format) {
    case "PEXR2002":
      return `PEXR2002
HDR:${dateStr}
REF:${reference}
AMT:${amount.toFixed(2)}
SND:Global Holdings Corp
RCV:Acme Services Ltd
PAY:ACH
DTL:Payment for services rendered
END:${dateStr}`;
    case "MT103":
      return `{1:F01BANKBEBBAXXX0548034306}{2:I103BANKDEFFXXXXN}{4:
:20:${reference}
:23B:CRED
:32A:${dateStr}USD${amount.toFixed(2)}
:50K:/********
Global Holdings Corp
123 Finance St
New York, NY
:59:/********
Acme Services Ltd
456 Commerce Ave
London, UK
:71A:SHA
-}`;
    case "ISO20022":
      return `<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.001.001.03">
  <CstmrCdtTrfInitn>
    <GrpHdr>
      <MsgId>${reference}</MsgId>
      <CreDtTm>${now.toISOString()}</CreDtTm>
      <NbOfTxs>1</NbOfTxs>
    </GrpHdr>
    <PmtInf>
      <PmtInfId>PMT-${dateStr}</PmtInfId>
      <PmtMtd>TRF</PmtMtd>
      <ReqdExctnDt>${dateStr}</ReqdExctnDt>
      <Dbtr>
        <Nm>Global Holdings Corp</Nm>
      </Dbtr>
      <DbtrAcct>
        <Id><IBAN>**********************</IBAN></Id>
      </DbtrAcct>
      <CdtTrfTxInf>
        <PmtId><EndToEndId>${reference}</EndToEndId></PmtId>
        <Amt><InstdAmt Ccy="USD">${amount.toFixed(2)}</InstdAmt></Amt>
        <Cdtr>
          <Nm>Acme Services Ltd</Nm>
        </Cdtr>
        <CdtrAcct>
          <Id><IBAN>**********************</IBAN></Id>
        </CdtrAcct>
      </CdtTrfTxInf>
    </PmtInf>
  </CstmrCdtTrfInitn>
</Document>`;
    default:
      throw new Error(`Unsupported payment file format: ${format}`);
  }
}
function generateSampleInvoiceFile(format) {
  const now = /* @__PURE__ */ new Date();
  const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
  const reference = `INV-${dateStr}-${Math.floor(Math.random() * 1e3).toString().padStart(3, "0")}`;
  const amount = Math.floor(Math.random() * 1e7) / 100;
  const dueDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1e3);
  const dueDateStr = `${dueDate.getFullYear()}${(dueDate.getMonth() + 1).toString().padStart(2, "0")}${dueDate.getDate().toString().padStart(2, "0")}`;
  switch (format) {
    case "EDI X12":
      return `ISA*00*          *00*          *ZZ*DigitalSystems *ZZ*TechSolutions I*${dateStr}*1130*U*00401*000000001*0*P*>
/* Invoice File - EDI X12 810 Format */
/* Common Reference: ${reference} */
GS*IN*DigitalSystems*TechSolutionsI*${dateStr}*1130*1*X*004010
ST*810*0001
BIG*${dateStr}*${reference}*${dateStr}*INV-0001
N1*ST*TechSolutions Inc.*92*000001
N3*San Francisco
N4*San Francisco*US*10000
N1*SE*DigitalSystems LLC*92*333001
N3*New York
N4*New York*US*20000
ITD*01*3*${dueDateStr}**30
IT1*1*1*EA*${amount.toFixed(2)}**BP*SWL-2023
TDS*${amount.toFixed(2).replace(".", "")}
CTT*1
SE*15*0001
GE*1*1
IEA*1*000000001`;
    case "ISO20022":
      return `<?xml version="1.0" encoding="UTF-8"?>
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pain.008.001.02">
  <CstmrInvcData>
    <GrpHdr>
      <MsgId>${reference}</MsgId>
      <CreDtTm>${now.toISOString()}</CreDtTm>
    </GrpHdr>
    <Invc>
      <InvcId>${reference}</InvcId>
      <IsseDt>${now.toISOString().split("T")[0]}</IsseDt>
      <DuePyblDt>${dueDate.toISOString().split("T")[0]}</DuePyblDt>
      <TtlInvcAmt Ccy="USD">${amount.toFixed(2)}</TtlInvcAmt>
      <Dbtr>
        <Nm>Tech Solutions Inc</Nm>
        <PstlAdr>
          <StrtNm>123 Tech Blvd</StrtNm>
          <TwnNm>Silicon Valley</TwnNm>
          <Ctry>US</Ctry>
          <PstCd>94000</PstCd>
        </PstlAdr>
        <CtctDtls>
          <PhneNb>+1-555-TECH-123</PhneNb>
          <EmailAdr><EMAIL></EmailAdr>
        </CtctDtls>
      </Dbtr>
      <Cdtr>
        <Nm>Global Holdings Corp</Nm>
        <PstlAdr>
          <StrtNm>456 Corporate Plaza</StrtNm>
          <TwnNm>New York</TwnNm>
          <Ctry>US</Ctry>
          <PstCd>10001</PstCd>
        </PstlAdr>
      </Cdtr>
      <RmtInf>
        <AddtlInf>Professional services rendered</AddtlInf>
      </RmtInf>
      <InvcItmGrp>
        <ItemDesc>Consulting Services</ItemDesc>
        <ItemQty>40</ItemQty>
        <ItemUnitPrc Ccy="USD">${(amount / 40).toFixed(2)}</ItemUnitPrc>
        <ItemSum Ccy="USD">${amount.toFixed(2)}</ItemSum>
      </InvcItmGrp>
      <InvcNotes>Thank you for your business!</InvcNotes>
    </Invc>
  </CstmrInvcData>
</Document>`;
    default:
      throw new Error(`Unsupported invoice file format: ${format}`);
  }
}

// server/utils/fileGenerators.ts
function generateRemittanceFile(payment, invoice, format) {
  switch (format) {
    case "MT940":
      return generateMT940(payment, invoice);
    case "BAI2":
      return generateBAI2(payment, invoice);
    case "ISO20022":
      return generateISO20022Remittance(payment, invoice);
    default:
      throw new Error(`Unsupported remittance format: ${format}`);
  }
}
function generateSampleRemittanceFile(format) {
  const mockPayment = {
    id: 0,
    reference: `SAMPLE-${(/* @__PURE__ */ new Date()).getTime()}`,
    amount: 1250.75,
    sender: "Sample Corporation",
    recipient: "Test Company Ltd",
    recipient_address: "123 Business Street, Finance District, 12345",
    status: "sent",
    file_type: "SAMPLE",
    file_content: "",
    created_at: /* @__PURE__ */ new Date(),
    remittance_id: null,
    approved: true,
    sent_at: /* @__PURE__ */ new Date(),
    remittance_generated: false
  };
  const mockInvoice = {
    id: 0,
    reference: `INV-SAMPLE-${(/* @__PURE__ */ new Date()).getTime()}`,
    customer: "Test Company Ltd",
    amount: 1250.75,
    description: "Sample invoice for demonstration",
    status: "paid",
    due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1e3),
    created_at: /* @__PURE__ */ new Date(),
    payment_id: null,
    remittance_id: null
  };
  return generateRemittanceFile(mockPayment, mockInvoice, format);
}
function generateMT940(payment, invoice) {
  const now = /* @__PURE__ */ new Date();
  const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
  const amountFormatted = payment.amount.toFixed(2).replace(".", ",");
  let mt940 = `:20:${payment.reference}
`;
  mt940 += `:25:********
`;
  mt940 += `:28C:1/1
`;
  mt940 += `:60F:C${dateStr}USD${amountFormatted}
`;
  mt940 += `:61:${dateStr}C${amountFormatted}NTRF
`;
  mt940 += `:86:Payment reference ${payment.reference}
`;
  if (invoice) {
    mt940 += `Invoice ${invoice.reference}
`;
  }
  mt940 += `From: ${payment.sender}
`;
  mt940 += `To: ${payment.recipient}
`;
  if ("recipient_address" in payment && payment.recipient_address) {
    mt940 += `Address: ${payment.recipient_address}
`;
  }
  mt940 += `:62F:C${dateStr}USD${amountFormatted}
`;
  return mt940;
}
function generateBAI2(payment, invoice) {
  const now = /* @__PURE__ */ new Date();
  const dateStr = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}`;
  const amountFormatted = Math.round(payment.amount * 100).toString();
  let bai2 = `01,${dateStr},${dateStr},${now.getHours()}${now.getMinutes()},001,${payment.sender.substring(0, 8)},,,1
`;
  bai2 += `02,********,1,USD,4,${dateStr},${dateStr},,,01
`;
  bai2 += `03,${amountFormatted},,,${payment.reference},${payment.sender},${payment.recipient}
`;
  if ("recipient_address" in payment && payment.recipient_address) {
    bai2 += `88,RCPT_ADR,${payment.recipient_address}
`;
  }
  if (invoice) {
    bai2 += `16,${invoice.reference},Invoice Payment
`;
  }
  bai2 += `49,1,${amountFormatted}
`;
  bai2 += `98,1,1,${amountFormatted}
`;
  bai2 += `99,1,${amountFormatted}
`;
  return bai2;
}
function generateISO20022Remittance(payment, invoice) {
  const now = /* @__PURE__ */ new Date();
  let xml = `<?xml version="1.0" encoding="UTF-8"?>
`;
  xml += `<Document xmlns="urn:iso:std:iso:20022:tech:xsd:camt.054.001.04">
`;
  xml += `  <BkToCstmrDbtCdtNtfctn>
`;
  xml += `    <GrpHdr>
`;
  xml += `      <MsgId>${payment.reference}</MsgId>
`;
  xml += `      <CreDtTm>${now.toISOString()}</CreDtTm>
`;
  xml += `    </GrpHdr>
`;
  xml += `    <Ntfctn>
`;
  xml += `      <Id>REMIT-${payment.reference}</Id>
`;
  xml += `      <CreDtTm>${now.toISOString()}</CreDtTm>
`;
  xml += `      <Acct>
`;
  xml += `        <Id><IBAN>**********************</IBAN></Id>
`;
  xml += `        <Ownr><Nm>${payment.recipient}</Nm></Ownr>
`;
  xml += `      </Acct>
`;
  xml += `      <Ntry>
`;
  xml += `        <Amt Ccy="USD">${payment.amount.toFixed(2)}</Amt>
`;
  xml += `        <CdtDbtInd>CRDT</CdtDbtInd>
`;
  xml += `        <RmtInf>
`;
  xml += `          <Strd>
`;
  xml += `            <RfrdDocInf>
`;
  xml += `              <Tp><CdOrPrtry><Cd>CINV</Cd></CdOrPrtry></Tp>
`;
  xml += `              <Nb>${payment.reference}</Nb>
`;
  xml += `            </RfrdDocInf>
`;
  if (invoice) {
    xml += `            <RfrdDocAmt><DuePyblAmt Ccy="USD">${invoice.amount.toFixed(2)}</DuePyblAmt></RfrdDocAmt>
`;
    xml += `            <CdtrRefInf><Ref>${invoice.reference}</Ref></CdtrRefInf>
`;
  }
  xml += `          </Strd>
`;
  xml += `        </RmtInf>
`;
  xml += `        <AddtlNtryInf>Payment from ${payment.sender}</AddtlNtryInf>
`;
  xml += `      </Ntry>
`;
  xml += `    </Ntfctn>
`;
  xml += `  </BkToCstmrDbtCdtNtfctn>
`;
  xml += `</Document>`;
  return xml;
}

// server/routes.ts
import multer from "multer";
import { eq as eq2 } from "drizzle-orm";

// server/utils/networkSimulator.ts
var NetworkSimulator = class {
  pendingOperations;
  constructor() {
    this.pendingOperations = /* @__PURE__ */ new Map();
  }
  /**
   * Simulates sending a payment with a network delay
   * @param paymentId The ID of the payment being sent
   * @param callback Function to call after the delay
   * @param delay Delay in milliseconds (defaults to 5000ms/5s)
   */
  async sendPayment(paymentId, callback, delay = 5e3) {
    const operationId = `send_payment_${paymentId}_${Date.now()}`;
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(async () => {
        try {
          await callback();
          resolve();
        } catch (error) {
          reject(error);
        } finally {
          this.pendingOperations.delete(operationId);
        }
      }, delay);
      this.pendingOperations.set(operationId, timeoutId);
    });
  }
  /**
   * Simulates receiving a payment with a network delay
   * @param paymentId The ID of the payment being received
   * @param callback Function to call after the delay
   * @param delay Delay in milliseconds (defaults to 3000ms/3s)
   */
  async receivePayment(paymentId, callback, delay = 3e3) {
    const operationId = `receive_payment_${paymentId}_${Date.now()}`;
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(async () => {
        try {
          await callback();
          resolve();
        } catch (error) {
          reject(error);
        } finally {
          this.pendingOperations.delete(operationId);
        }
      }, delay);
      this.pendingOperations.set(operationId, timeoutId);
    });
  }
  /**
   * Cancels a pending operation
   * @param operationId The ID of the operation to cancel
   */
  cancelOperation(operationId) {
    const timeoutId = this.pendingOperations.get(operationId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.pendingOperations.delete(operationId);
      return true;
    }
    return false;
  }
  /**
   * Lists all pending operations
   */
  getPendingOperations() {
    return Array.from(this.pendingOperations.keys());
  }
};
var networkSimulator = new NetworkSimulator();

// server/routes.ts
var dummySimulator = {
  sendPayment: async (id, callback) => {
    return callback();
  },
  receivePayment: async (id, callback) => {
    return callback();
  }
};
var simulator = process.env.SIMULATOR_ENABLED === "true" ? networkSimulator : dummySimulator;
var upload = multer({ storage: multer.memoryStorage() });
async function registerRoutes(app2) {
  const httpServer = createServer(app2);
  app2.get("/api/payments", async (req, res) => {
    try {
      const payments2 = await storage.getAllPayments();
      res.json(payments2);
    } catch (error) {
      console.error("Error fetching payments:", error);
      res.status(500).json({ error: "Failed to fetch payments" });
    }
  });
  app2.get("/api/payments/:id", async (req, res) => {
    try {
      const payment = await storage.getPayment(parseInt(req.params.id));
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      res.json(payment);
    } catch (error) {
      console.error("Error fetching payment:", error);
      res.status(500).json({ error: "Failed to fetch payment" });
    }
  });
  app2.post("/api/payments/:id/approve", async (req, res) => {
    try {
      const { signature, message } = req.body;
      const payment = await storage.approvePayment(
        parseInt(req.params.id),
        signature,
        message
      );
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      res.json(payment);
    } catch (error) {
      console.error("Error approving payment:", error);
      res.status(500).json({ error: "Failed to approve payment" });
    }
  });
  app2.post("/api/payments/:id/revoke", async (req, res) => {
    try {
      const payment = await storage.revokePaymentApproval(parseInt(req.params.id));
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      res.json(payment);
    } catch (error) {
      console.error("Error revoking payment approval:", error);
      res.status(500).json({ error: "Failed to revoke payment approval" });
    }
  });
  app2.post("/api/payments/:id/send", async (req, res) => {
    try {
      const paymentId = parseInt(req.params.id);
      const payment = await storage.getPayment(paymentId);
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      if (!payment.approved) {
        return res.status(400).json({ error: "Payment must be approved before sending" });
      }
      res.json({ status: "pending", message: "Payment is being processed" });
      await simulator.sendPayment(paymentId, async () => {
        try {
          const updatedPayment = await storage.sendPayment(paymentId);
          console.log("Updated payment to PAID:", updatedPayment);
          if (updatedPayment) {
            console.log(`Payment #${paymentId} marked as sent successfully`);
          }
        } catch (error) {
          console.error("Error in payment callback:", error);
        }
      });
    } catch (error) {
      console.error("Error sending payment:", error);
    }
  });
  app2.post("/api/payments/:id/generate-remittance", async (req, res) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const parsedBody = z.object({ format: formatSchema }).parse(req.body);
      const format = parsedBody.format;
      const paymentId = parseInt(req.params.id);
      const payment = await storage.getPayment(paymentId);
      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }
      if (!payment.sent_at) {
        return res.status(400).json({ error: "Payment must be sent before generating remittance" });
      }
      const remittanceContent = generateRemittanceFile(payment, null, format);
      const filePath = await storage.saveFile(remittanceContent, "remittance", format);
      const remittance = await storage.createRemittance({
        payment_id: payment.id,
        format,
        file_path: filePath,
        amount: payment.amount,
        sender: payment.sender,
        recipient: "Your Company",
        // Add a default recipient value
        reference: payment.reference
      });
      await storage.updatePaymentRemittanceStatus(payment.id, remittance.id);
      res.json(remittance);
    } catch (error) {
      console.error("Error generating remittance:", error);
      res.status(500).json({ error: "Failed to generate remittance" });
    }
  });
  app2.post("/api/upload_payment", upload.single("file"), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }
      const fileContent = req.file.buffer.toString("utf8");
      const paymentData = processPaymentFile(fileContent);
      console.log("Payment data parsed successfully:", paymentData.reference);
      const existingPayment = await storage.getPaymentByReference(paymentData.reference);
      if (existingPayment) {
        return res.status(400).json({
          error: `Payment with reference number "${paymentData.reference}" already exists (ID: ${existingPayment.id})`
        });
      }
      const payment = await storage.createPayment(paymentData);
      console.log("Payment created successfully with ID:", payment.id);
      res.json(payment);
    } catch (error) {
      console.error("Error uploading payment file:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to upload payment file" });
    }
  });
  app2.get("/api/sample_payment/:format", async (req, res) => {
    try {
      const formatSchema = z.enum(fileFormats.payment);
      const format = formatSchema.parse(req.params.format);
      const sampleContent = generateSamplePaymentFile(format);
      res.setHeader("Content-Type", "text/plain");
      res.setHeader("Content-Disposition", `attachment; filename=sample_payment_${format}.txt`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample payment file:", error);
      res.status(500).json({ error: "Failed to generate sample payment file" });
    }
  });
  app2.get("/api/remittances/:id", async (req, res) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      res.json(remittance);
    } catch (error) {
      console.error("Error fetching remittance:", error);
      res.status(500).json({ error: "Failed to fetch remittance" });
    }
  });
  app2.get("/api/remittances/:id/download", async (req, res) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      const fileContent = await storage.getFileContent(remittance.file_path);
      res.setHeader("Content-Type", "text/plain");
      res.setHeader("Content-Disposition", `attachment; filename=remittance_${remittance.id}_${remittance.format}.txt`);
      res.send(fileContent);
    } catch (error) {
      console.error("Error downloading remittance:", error);
      res.status(500).json({ error: "Failed to download remittance" });
    }
  });
  app2.get("/api/remittances/:id/debug", async (req, res) => {
    try {
      const remittance = await storage.getRemittance(parseInt(req.params.id));
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      const fileContent = await storage.getFileContent(remittance.file_path);
      res.setHeader("Content-Type", "text/plain");
      res.send(fileContent);
    } catch (error) {
      console.error("Error debugging remittance:", error);
      res.status(500).json({ error: "Failed to debug remittance" });
    }
  });
  app2.get("/api/invoices", async (req, res) => {
    try {
      await updateOverdueInvoices();
      const status = req.query.status;
      const invoices2 = status ? await storage.getInvoicesByStatus(status) : await storage.getAllInvoices();
      res.json(invoices2);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      res.status(500).json({ error: "Failed to fetch invoices" });
    }
  });
  async function updateOverdueInvoices() {
    try {
      const openInvoices = await storage.getInvoicesByStatus("Open");
      const today = /* @__PURE__ */ new Date();
      for (const invoice of openInvoices) {
        const dueDate = new Date(invoice.due_date);
        if (dueDate < today) {
          console.log(`Updating invoice ${invoice.id} status to Overdue (due date: ${dueDate.toDateString()})`);
          await storage.updateInvoiceStatus(invoice.id, "Overdue");
        }
      }
    } catch (error) {
      console.error("Error updating overdue invoices:", error);
    }
  }
  app2.get("/api/invoices/:id", async (req, res) => {
    try {
      const invoice = await storage.getInvoice(parseInt(req.params.id));
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      res.json(invoice);
    } catch (error) {
      console.error("Error fetching invoice:", error);
      res.status(500).json({ error: "Failed to fetch invoice" });
    }
  });
  app2.post("/api/invoices", async (req, res) => {
    try {
      const invoiceData = insertInvoiceSchema.parse(req.body);
      const invoice = await storage.createInvoice(invoiceData);
      res.json(invoice);
    } catch (error) {
      console.error("Error creating invoice:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to create invoice" });
    }
  });
  app2.post("/api/invoices/:id/update-status", async (req, res) => {
    try {
      const statusSchema = z.enum(["Open", "Overdue", "Paid", "Remitted"]);
      const { status } = z.object({ status: statusSchema }).parse(req.body);
      const invoice = await storage.updateInvoiceStatus(parseInt(req.params.id), status);
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      res.json(invoice);
    } catch (error) {
      console.error("Error updating invoice status:", error);
      res.status(500).json({ error: "Failed to update invoice status" });
    }
  });
  app2.post("/api/invoices/:id/generate-remittance", async (req, res) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const parsedBody = z.object({ format: formatSchema }).parse(req.body);
      const format = parsedBody.format;
      const invoiceId = parseInt(req.params.id);
      const invoice = await storage.getInvoice(invoiceId);
      if (!invoice) {
        return res.status(404).json({ error: "Invoice not found" });
      }
      if (!["Paid", "Remitted"].includes(invoice.status) || !invoice.payment_id) {
        return res.status(400).json({ error: "Invoice must be paid before generating remittance" });
      }
      const payment = await storage.getReceivedPayment(invoice.payment_id);
      if (!payment) {
        return res.status(400).json({ error: "Associated payment not found" });
      }
      const remittanceContent = generateRemittanceFile(payment, invoice, format);
      const filePath = await storage.saveFile(remittanceContent, "invoice_remittance", format);
      const remittance = await storage.createRemittance({
        payment_id: payment.id,
        format,
        file_path: filePath,
        amount: invoice.amount,
        sender: payment.sender,
        recipient: invoice.customer,
        // Use the invoice customer as recipient
        reference: invoice.reference
      });
      await storage.updateReceivedPaymentRemittanceStatus(payment.id, remittance.id);
      await storage.updateInvoiceStatus(invoiceId, "Remitted");
      const [updatedInvoice] = await db.update(invoices).set({
        remittance_id: remittance.id,
        remittance_generated: true,
        remittance_generated_at: /* @__PURE__ */ new Date()
      }).where(eq2(invoices.id, invoiceId)).returning();
      res.json(remittance);
    } catch (error) {
      console.error("Error generating invoice remittance:", error);
      res.status(500).json({ error: "Failed to generate invoice remittance" });
    }
  });
  app2.get("/api/invoices/:id/download-remittance", async (req, res) => {
    try {
      const invoice = await storage.getInvoice(parseInt(req.params.id));
      if (!invoice || !invoice.remittance_id) {
        return res.status(404).json({ error: "Invoice remittance not found" });
      }
      const remittance = await storage.getRemittance(invoice.remittance_id);
      if (!remittance) {
        return res.status(404).json({ error: "Remittance not found" });
      }
      const fileContent = await storage.getFileContent(remittance.file_path);
      res.setHeader("Content-Type", "text/plain");
      res.setHeader("Content-Disposition", `attachment; filename=invoice_remittance_${invoice.id}_${remittance.format}.txt`);
      res.send(fileContent);
    } catch (error) {
      console.error("Error downloading invoice remittance:", error);
      res.status(500).json({ error: "Failed to download invoice remittance" });
    }
  });
  app2.post("/api/upload_invoice", upload.single("file"), async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ error: "No file uploaded" });
      }
      const fileContent = req.file.buffer.toString("utf8");
      const detectedFormat = detectFileFormat(fileContent);
      console.log(`Detected format in upload_invoice: ${JSON.stringify(detectedFormat)}`);
      let invoiceData;
      if (detectedFormat.type === "payment") {
        console.log(`File detected as payment, but uploaded to invoice endpoint. Attempting to treat as invoice...`);
        try {
          if (fileContent.includes("ISA*") || fileContent.includes("GS*")) {
            console.log("Trying to process as EDI X12 invoice");
            invoiceData = processInvoiceFile(fileContent, "EDI X12");
          } else if (fileContent.includes("<?xml")) {
            console.log("Trying to process as ISO20022 invoice");
            invoiceData = processInvoiceFile(fileContent, "ISO20022");
          } else {
            console.log("Converting PEXR2002-like format to ISO20022 for invoice processing");
            const lines = fileContent.trim().split("\n");
            const reference = lines.find((l) => l.startsWith("REF:"))?.substring(4).trim() || `INV-${(/* @__PURE__ */ new Date()).toISOString().split("T")[0].replace(/-/g, "")}-${Math.floor(Math.random() * 1e3)}`;
            const amountStr = lines.find((l) => l.startsWith("AMT:"))?.substring(4).trim() || "1000.00";
            const amount = parseFloat(amountStr);
            const customer = lines.find((l) => l.startsWith("SND:"))?.substring(4).trim() || "Unknown Customer";
            const mockXml = `<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <CstmrInvcData>
    <Invc>
      <InvcId>${reference}</InvcId>
      <TtlInvcAmt>${amount}</TtlInvcAmt>
      <Dbtr><Nm>${customer}</Nm></Dbtr>
      <RmtInf><AddtlInf>Invoice generated from file upload</AddtlInf></RmtInf>
    </Invc>
  </CstmrInvcData>
</Document>`;
            invoiceData = processInvoiceFile(mockXml, "ISO20022");
          }
        } catch (error) {
          console.error("Failed to convert payment format to invoice:", error);
          throw new Error(`Could not process file as invoice: ${error instanceof Error ? error.message : String(error)}`);
        }
      } else {
        invoiceData = processInvoiceFile(fileContent);
      }
      console.log("Invoice data parsed successfully:", invoiceData.reference);
      const existingInvoice = await storage.getInvoiceByReference(invoiceData.reference);
      if (existingInvoice) {
        return res.status(400).json({
          error: `Invoice with reference number "${invoiceData.reference}" already exists (ID: ${existingInvoice.id})`
        });
      }
      const invoice = await storage.createInvoice(invoiceData);
      const allReceivedPayments = await storage.getAllReceivedPayments();
      const matchingPayment = allReceivedPayments.find(
        (payment) => (
          // Exact match for reference numbers
          payment.reference === invoice.reference || // Handle REF prefix variations but keep the unique part
          payment.reference.includes("-") && invoice.reference.includes("-") && payment.reference.split("-").slice(1).join("-") === invoice.reference.split("-").slice(1).join("-")
        )
      );
      if (matchingPayment && !matchingPayment.invoice_id) {
        console.log(`Auto-linking invoice ${invoice.id} to received payment ${matchingPayment.id}`);
        await storage.linkReceivedPaymentToInvoice(matchingPayment.id, invoice.id);
        await storage.updateInvoiceStatus(invoice.id, "Paid");
        const updatedInvoice = await storage.getInvoice(invoice.id);
        res.json(updatedInvoice);
      } else {
        res.json(invoice);
      }
    } catch (error) {
      console.error("Error uploading invoice file:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to upload invoice file" });
    }
  });
  app2.get("/api/sample_invoice/:format", async (req, res) => {
    try {
      const formatSchema = z.enum(fileFormats.invoice);
      const format = formatSchema.parse(req.params.format);
      const sampleContent = generateSampleInvoiceFile(format);
      console.log(`Generated sample ${format} invoice content (first 100 chars): ${sampleContent.substring(0, 100)}`);
      const detectedFormat = detectFileFormat(sampleContent);
      console.log(`Sample invoice detection result: ${JSON.stringify(detectedFormat)}`);
      if (detectedFormat.type !== "invoice") {
        console.error(`Warning: Generated sample ${format} invoice was detected as ${detectedFormat.type}`);
      }
      let filename = `sample_invoice_${format}.txt`;
      let contentType = "text/plain";
      if (format === "ISO20022") {
        filename = `sample_invoice_${format}.xml`;
        contentType = "application/xml";
      }
      res.setHeader("Content-Type", contentType);
      res.setHeader("Content-Disposition", `attachment; filename=${filename}`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample invoice file:", error);
      res.status(500).json({ error: "Failed to generate sample invoice file" });
    }
  });
  app2.get("/api/sample_remittance/:format", async (req, res) => {
    try {
      const formatSchema = z.enum(fileFormats.remittance);
      const format = formatSchema.parse(req.params.format);
      const sampleContent = generateSampleRemittanceFile(format);
      res.setHeader("Content-Type", "text/plain");
      res.setHeader("Content-Disposition", `attachment; filename=sample_remittance_${format}.txt`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample remittance file:", error);
      res.status(500).json({ error: "Failed to generate sample remittance file" });
    }
  });
  app2.get("/api/received-payments", async (req, res) => {
    try {
      const receivedPayments2 = await storage.getAllReceivedPayments();
      res.json(receivedPayments2);
    } catch (error) {
      console.error("Error fetching received payments:", error);
      res.status(500).json({ error: "Failed to fetch received payments" });
    }
  });
  app2.get("/api/received-payments/:id", async (req, res) => {
    try {
      const receivedPayment = await storage.getReceivedPayment(parseInt(req.params.id));
      if (!receivedPayment) {
        return res.status(404).json({ error: "Received payment not found" });
      }
      res.json(receivedPayment);
    } catch (error) {
      console.error("Error fetching received payment:", error);
      res.status(500).json({ error: "Failed to fetch received payment" });
    }
  });
  app2.post("/api/received-payments/:id/link-invoice", async (req, res) => {
    try {
      const { invoiceId } = z.object({ invoiceId: z.number() }).parse(req.body);
      const paymentId = parseInt(req.params.id);
      const receivedPayment = await storage.linkReceivedPaymentToInvoice(paymentId, invoiceId);
      if (!receivedPayment) {
        return res.status(404).json({ error: "Received payment or invoice not found" });
      }
      const updatedInvoice = await storage.updateInvoiceStatus(invoiceId, "Paid");
      if (!updatedInvoice) {
        console.warn(`Failed to update invoice ${invoiceId} status to Paid after linking to payment ${paymentId}`);
      } else {
        console.log(`Successfully updated invoice ${invoiceId} status to Paid after linking to payment ${paymentId}`);
      }
      res.json({
        receivedPayment,
        invoice: updatedInvoice
      });
    } catch (error) {
      console.error("Error linking received payment to invoice:", error);
      res.status(500).json({ error: "Failed to link received payment to invoice" });
    }
  });
  app2.post("/api/simulate_received_payment", async (req, res) => {
    try {
      const paymentData = {
        reference: req.body.reference,
        amount: req.body.amount,
        sender: req.body.sender,
        recipient: req.body.recipient || "Your Company",
        invoice_id: req.body.invoice_id || null
      };
      const receivedPayment = await storage.createReceivedPayment(paymentData);
      console.log(`Created received payment ${receivedPayment.id} with reference ${receivedPayment.reference}`);
      res.json(receivedPayment);
    } catch (error) {
      console.error("Error creating received payment:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to create received payment" });
    }
  });
  app2.post("/api/import/received-payment", async (req, res) => {
    try {
      const { sender, from, amount, reference } = req.body;
      const senderName = sender || from;
      if (!senderName) {
        return res.status(400).json({ error: "Sender name is required" });
      }
      const payment = await storage.markPaymentReceived({ from: senderName, amount, reference });
      if (!payment) return res.status(404).send("Reference not found");
      res.json(payment);
    } catch (error) {
      console.error("Error importing received payment:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to import received payment" });
    }
  });
  app2.post("/api/import/receipt", async (req, res) => {
    try {
      const { account, amount, reference } = req.body;
      const pay = await storage.markReceiptImported({ account, amount, reference });
      if (!pay) return res.status(404).send("Reference not found");
      res.json(pay);
    } catch (error) {
      console.error("Error importing receipt:", error);
      res.status(500).json({ error: error instanceof Error ? error.message : "Failed to import receipt" });
    }
  });
  app2.get("/api/admin/organization", async (req, res) => {
    try {
      const organization = {
        legalName: "Global Logistics Ltd.",
        displayName: "GlobalLogi",
        accountNumber: "GL-001",
        emailDomain: "globallogistics.com",
        industry: "Logistics & Supply Chain",
        fiscalCurrency: "USD",
        erpBaseUrl: "https://sap-gl-prod.example.com",
        erpApiKey: "demo-erp-api-key"
      };
      res.json(organization);
    } catch (error) {
      console.error("Error fetching organization:", error);
      res.status(500).json({ error: "Failed to fetch organization" });
    }
  });
  app2.put("/api/admin/organization", async (req, res) => {
    try {
      const organizationData = req.body;
      res.json(organizationData);
    } catch (error) {
      console.error("Error updating organization:", error);
      res.status(500).json({ error: "Failed to update organization" });
    }
  });
  app2.get("/api/admin/members", async (req, res) => {
    try {
      const members = [
        {
          id: "1",
          email: "<EMAIL>",
          firstName: "Maria",
          lastName: "Hughes",
          role: "Approver",
          tier: "Tier 1",
          filters: ["Boeing", "Mod Pizza"],
          status: "Active"
        },
        {
          id: "2",
          email: "<EMAIL>",
          firstName: "David",
          lastName: "Nguyen",
          role: "Approver",
          tier: "Tier 2",
          filters: ["Kratos Defense"],
          status: "Active"
        },
        {
          id: "3",
          email: "<EMAIL>",
          firstName: "Alex",
          lastName: "Choi",
          role: "Accounts Payable",
          filters: ["All"],
          status: "Active"
        },
        {
          id: "4",
          email: "<EMAIL>",
          firstName: "Sara",
          lastName: "Wilson",
          role: "Accounts Receivable",
          filters: ["Bumble Bee Foods"],
          status: "Pending"
        }
      ];
      res.json(members);
    } catch (error) {
      console.error("Error fetching members:", error);
      res.status(500).json({ error: "Failed to fetch members" });
    }
  });
  app2.post("/api/admin/members", async (req, res) => {
    try {
      const memberData = {
        id: Date.now().toString(),
        ...req.body,
        status: "Pending"
      };
      res.json(memberData);
    } catch (error) {
      console.error("Error creating member:", error);
      res.status(500).json({ error: "Failed to create member" });
    }
  });
  app2.put("/api/admin/members/:id", async (req, res) => {
    try {
      const memberData = {
        id: req.params.id,
        ...req.body
      };
      res.json(memberData);
    } catch (error) {
      console.error("Error updating member:", error);
      res.status(500).json({ error: "Failed to update member" });
    }
  });
  app2.patch("/api/admin/members/:id/disable", async (req, res) => {
    try {
      const memberData = {
        id: req.params.id,
        status: "Disabled"
      };
      res.json(memberData);
    } catch (error) {
      console.error("Error disabling member:", error);
      res.status(500).json({ error: "Failed to disable member" });
    }
  });
  app2.post("/api/admin/members/:id/resend-invite", async (req, res) => {
    try {
      res.json({ message: "Invite resent successfully" });
    } catch (error) {
      console.error("Error resending invite:", error);
      res.status(500).json({ error: "Failed to resend invite" });
    }
  });
  app2.get("/api/admin/approval-predicate", async (req, res) => {
    try {
      const predicate = {
        tier1: "<EMAIL>",
        tier2: "<EMAIL>",
        accountOwner: "<EMAIL>"
      };
      res.json(predicate);
    } catch (error) {
      console.error("Error fetching approval predicate:", error);
      res.status(500).json({ error: "Failed to fetch approval predicate" });
    }
  });
  app2.put("/api/admin/approval-predicate", async (req, res) => {
    try {
      const predicateData = req.body;
      res.json(predicateData);
    } catch (error) {
      console.error("Error updating approval predicate:", error);
      res.status(500).json({ error: "Failed to update approval predicate" });
    }
  });
  app2.post("/api/admin/workflow/import", async (req, res) => {
    try {
      const { vendor, content, filename } = req.body;
      const importResult = {
        vendor,
        format: filename.split(".").pop(),
        workflows: [
          {
            name: `${vendor} Workflow`,
            description: `Imported from ${vendor}`,
            steps: [
              {
                name: "Manager Approval",
                sequence: 1,
                conditions: [],
                approvers: ["manager"],
                mode: "SERIAL"
              }
            ],
            metadata: { imported: true }
          }
        ],
        fieldMappings: {
          "amount": "amount",
          "currency": "currency",
          "supplier": "supplier.id"
        },
        warnings: [],
        errors: []
      };
      res.json(importResult);
    } catch (error) {
      console.error("Error importing workflow:", error);
      res.status(500).json({ error: "Failed to import workflow" });
    }
  });
  app2.post("/api/admin/workflow/import/:vendor/pull", async (req, res) => {
    try {
      const { vendor } = req.params;
      const result = {
        success: true,
        workflowsImported: 3,
        lastSync: (/* @__PURE__ */ new Date()).toISOString()
      };
      res.json(result);
    } catch (error) {
      console.error("Error pulling workflows:", error);
      res.status(500).json({ error: "Failed to pull workflows" });
    }
  });
  app2.get("/api/admin/workflow/definitions", async (req, res) => {
    try {
      const definitions = [
        {
          id: "wf-001",
          name: "Standard Invoice Approval",
          description: "Standard approval workflow for invoices",
          version: "1.0.0",
          documentType: "INVOICE",
          status: "ACTIVE",
          steps: [
            {
              id: "step-1",
              sequence: 1,
              name: "Manager Approval",
              predicate: {
                type: "comparison",
                field: "amount",
                operator: "lt",
                value: 1e4
              },
              approverRoleIds: ["manager"],
              approvalMode: "SERIAL",
              timeoutHours: 24
            }
          ],
          createdAt: "2024-01-15T10:00:00Z",
          updatedAt: "2024-01-20T14:30:00Z",
          createdBy: "admin"
        }
      ];
      res.json(definitions);
    } catch (error) {
      console.error("Error fetching workflow definitions:", error);
      res.status(500).json({ error: "Failed to fetch workflow definitions" });
    }
  });
  app2.put("/api/admin/workflow/definitions/:id", async (req, res) => {
    try {
      const { id } = req.params;
      const workflowData = req.body;
      const updatedWorkflow = {
        ...workflowData,
        id,
        updatedAt: (/* @__PURE__ */ new Date()).toISOString()
      };
      res.json(updatedWorkflow);
    } catch (error) {
      console.error("Error updating workflow definition:", error);
      res.status(500).json({ error: "Failed to update workflow definition" });
    }
  });
  app2.post("/api/engine/evaluate", async (req, res) => {
    try {
      const { workflowId, document } = req.body;
      const result = {
        workflowId,
        documentId: document.id,
        approvers: [
          {
            stepId: "step-1",
            sequence: 1,
            approverUserIds: ["manager-1"],
            approvalMode: "SERIAL",
            status: "PENDING",
            requiredApprovals: 1,
            currentApprovals: 0
          }
        ],
        autoApproved: false,
        estimatedCompletionTime: 24,
        evaluatedAt: (/* @__PURE__ */ new Date()).toISOString(),
        evaluationTimeMs: 2.5
      };
      res.json(result);
    } catch (error) {
      console.error("Error evaluating workflow:", error);
      res.status(500).json({ error: "Failed to evaluate workflow" });
    }
  });
  app2.post("/api/admin/users/import", async (req, res) => {
    try {
      const { vendor, content, filename } = req.body;
      const importResult = {
        vendor,
        format: filename?.split(".").pop() || "unknown",
        users: [
          {
            rawData: { imported: true },
            email: "<EMAIL>",
            firstName: "John",
            lastName: "Doe",
            erpUserId: "JDOE",
            erpRoles: ["F_BKPF_APP"],
            status: "ACTIVE"
          },
          {
            rawData: { imported: true },
            email: "<EMAIL>",
            firstName: "Jane",
            lastName: "Smith",
            erpUserId: "JSMITH",
            erpRoles: ["F_BKPF_BUK"],
            status: "ACTIVE"
          }
        ],
        fieldMappings: {
          "BNAME": "erpUserId",
          "SMTP_ADDR": "email",
          "NAME_FIRST": "firstName",
          "NAME_LAST": "lastName",
          "AGR_NAME": "erpRoleCodes"
        },
        warnings: [],
        errors: [],
        metadata: {
          fileName: filename,
          recordCount: 2,
          parsedAt: (/* @__PURE__ */ new Date()).toISOString()
        }
      };
      res.json(importResult);
    } catch (error) {
      console.error("Error importing users:", error);
      res.status(500).json({ error: "Failed to import users" });
    }
  });
  app2.post("/api/admin/users/import/:vendor/pull", async (req, res) => {
    try {
      const { vendor } = req.params;
      const credentials = req.body;
      const result = {
        vendor,
        format: "api",
        users: [
          {
            rawData: { apiPull: true },
            email: "<EMAIL>",
            firstName: "API",
            lastName: "User",
            erpUserId: "APIUSER",
            erpRoles: ["F_BKPF_APP"],
            status: "ACTIVE"
          }
        ],
        fieldMappings: {
          "user_id": "erpUserId",
          "email": "email",
          "first_name": "firstName",
          "last_name": "lastName",
          "roles": "erpRoleCodes"
        },
        warnings: [],
        errors: [],
        metadata: {
          apiEndpoint: credentials.baseUrl,
          recordCount: 1,
          pulledAt: (/* @__PURE__ */ new Date()).toISOString()
        }
      };
      res.json(result);
    } catch (error) {
      console.error("Error pulling users:", error);
      res.status(500).json({ error: "Failed to pull users from API" });
    }
  });
  app2.get("/api/admin/usersets", async (req, res) => {
    try {
      const userSets2 = [
        {
          id: "us-003",
          version: "1.2.0",
          name: "SAP User Import - December 2024",
          description: "Updated user roles and added new cost center assignments",
          vendor: "SAP",
          status: "ACTIVE",
          userCount: 47,
          changeLog: "Added 3 new users, updated 5 role assignments, removed 1 inactive user",
          createdAt: "2024-12-15T10:30:00Z",
          createdBy: "<EMAIL>",
          activatedAt: "2024-12-15T11:00:00Z"
        },
        {
          id: "us-002",
          version: "1.1.0",
          name: "Oracle User Sync - November 2024",
          description: "Quarterly user role synchronization from Oracle HCM",
          vendor: "Oracle",
          status: "ARCHIVED",
          userCount: 45,
          changeLog: "Updated 8 user roles, added 2 new approvers",
          createdAt: "2024-11-20T14:15:00Z",
          createdBy: "<EMAIL>",
          activatedAt: "2024-11-20T14:30:00Z"
        }
      ];
      res.json(userSets2);
    } catch (error) {
      console.error("Error fetching user sets:", error);
      res.status(500).json({ error: "Failed to fetch user sets" });
    }
  });
  app2.post("/api/admin/usersets/:id/rollback", async (req, res) => {
    try {
      const { id } = req.params;
      const result = {
        success: true,
        userSetId: id,
        rolledBackAt: (/* @__PURE__ */ new Date()).toISOString(),
        message: "User set rolled back successfully"
      };
      res.json(result);
    } catch (error) {
      console.error("Error rolling back user set:", error);
      res.status(500).json({ error: "Failed to rollback user set" });
    }
  });
  app2.post("/api/admin/users/mappings", async (req, res) => {
    try {
      const mappingData = req.body;
      const savedMapping = {
        id: `mapping-${Date.now()}`,
        ...mappingData,
        createdAt: (/* @__PURE__ */ new Date()).toISOString(),
        updatedAt: (/* @__PURE__ */ new Date()).toISOString(),
        createdBy: "<EMAIL>"
      };
      res.json(savedMapping);
    } catch (error) {
      console.error("Error saving user mapping:", error);
      res.status(500).json({ error: "Failed to save user mapping" });
    }
  });
  app2.get("/api/admin/users/sample/:vendor", async (req, res) => {
    try {
      const { vendor } = req.params;
      let sampleContent = "";
      let contentType = "text/csv";
      let filename = `${vendor.toLowerCase()}_users_sample.csv`;
      switch (vendor) {
        case "SAP":
          sampleContent = `BNAME,SMTP_ADDR,NAME_FIRST,NAME_LAST,AGR_NAME,KOSTL,BUKRS,USTYP
MHUGHES,<EMAIL>,Maria,Hughes,F_BKPF_APP,CC-1001,GL01,0
DNGUYEN,<EMAIL>,David,Nguyen,F_BKPF_MGR,CC-2001,GL01,0`;
          break;
        case "Oracle":
          sampleContent = `user_id,email,first_name,last_name,roles,cost_center,legal_entity,status
MHUGHES,<EMAIL>,Maria,Hughes,AP_APPROVAL_LEVEL1,CC-1001,US-WEST,ACTIVE
DNGUYEN,<EMAIL>,David,Nguyen,AP_APPROVAL_LEVEL2,CC-2001,US-EAST,ACTIVE`;
          break;
        case "Dynamics365":
          sampleContent = `systemuserid,internalemailaddress,firstname,lastname,roles,businessunitid,organizationid,isdisabled
{guid1},<EMAIL>,Maria,Hughes,Purchasing manager,BU-001,ORG-001,false
{guid2},<EMAIL>,David,Nguyen,Finance manager,BU-002,ORG-001,false`;
          break;
        case "NetSuite":
          sampleContent = `id,email,firstname,lastname,roles,department,subsidiary,isinactive
123,<EMAIL>,Maria,Hughes,Approver,Finance,1,false
124,<EMAIL>,David,Nguyen,Senior Approver,Finance,1,false`;
          break;
        default:
          throw new Error(`Unsupported vendor: ${vendor}`);
      }
      res.setHeader("Content-Type", contentType);
      res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
      res.send(sampleContent);
    } catch (error) {
      console.error("Error generating sample file:", error);
      res.status(500).json({ error: "Failed to generate sample file" });
    }
  });
  return httpServer;
}

// server/vite.ts
import express from "express";
import fs3 from "fs";
import path4 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import themePlugin from "@replit/vite-plugin-shadcn-theme-json";
import path3 from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
import { fileURLToPath } from "url";
var __filename = fileURLToPath(import.meta.url);
var __dirname = path3.dirname(__filename);
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    themePlugin(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  resolve: {
    alias: {
      "@": path3.resolve(__dirname, "client", "src"),
      "@shared": path3.resolve(__dirname, "shared"),
      "@assets": path3.resolve(__dirname, "attached_assets")
    }
  },
  root: path3.resolve(__dirname, "client"),
  build: {
    outDir: path3.resolve(__dirname, "dist/public"),
    emptyOutDir: true
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path4.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs3.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path4.resolve(import.meta.dirname, "public");
  if (!fs3.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path4.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path5 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path5.startsWith("/api")) {
      let logLine = `${req.method} ${path5} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5001;
  const isLocalDev = process.env.NODE_ENV === "development" && process.platform === "darwin";
  if (isLocalDev) {
    server.listen(port, () => {
      log(`serving on port ${port}`);
    });
  } else {
    server.listen({
      port,
      host: "0.0.0.0",
      reusePort: true
    }, () => {
      log(`serving on port ${port}`);
    });
  }
})();
