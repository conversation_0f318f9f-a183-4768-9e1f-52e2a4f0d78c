/**
 * Unit tests for useCurrentMetrics hook
 */

import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useCurrentMetrics } from '../useCurrentMetrics';

// Mock fetch globally
global.fetch = jest.fn();

// Mock demo data functions
jest.mock('../../lib/demoData', () => ({
  getDemoBankBalances: () => [
    { accountId: 'TEST-001', label: 'Test Bank', currency: 'USD', amount: 100000 }
  ],
  getDemoStablecoinBalances: () => [
    { wallet: '0x123', symbol: 'USDC', amount: 50000 }
  ],
  getDemoPaymentsToday: () => 5,
  calculateYTDFeeSavings: () => 15.5,
  getCurrentTouchlessRate: () => 85
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useCurrentMetrics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('fetches and returns current metrics successfully', async () => {
    // Mock successful API responses
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { accountId: 'JPM-001', label: 'JPMorgan', currency: 'USD', amount: 750000 }
        ]
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { wallet: '0x456', symbol: 'USDC', amount: 245000 }
        ]
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ count: 8, payments: [] })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: 1, recipient: 'Test Corp', amount: 10000 }
        ]
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: 1, customer: 'Test Customer', amount: 5000, status: 'Open' }
        ]
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { id: 1, sender: 'Test Sender', amount: 3000 }
        ]
      });

    const { result } = renderHook(() => useCurrentMetrics(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.metrics.bankBalances).toHaveLength(1);
    expect(result.current.metrics.bankBalances[0].amount).toBe(750000);
    expect(result.current.metrics.stablecoinBalances).toHaveLength(1);
    expect(result.current.metrics.paymentsToday).toBe(8);
    expect(result.current.metrics.paymentsAwaitingApproval).toHaveLength(1);
    expect(result.current.metrics.openInvoices).toHaveLength(1);
    expect(result.current.metrics.incomingPayments).toHaveLength(1);
  });

  it('falls back to demo data when API fails', async () => {
    // Mock failed API responses
    (fetch as jest.Mock)
      .mockRejectedValueOnce(new Error('Network error'))
      .mockRejectedValueOnce(new Error('Network error'))
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      });

    const { result } = renderHook(() => useCurrentMetrics(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should use demo data
    expect(result.current.metrics.bankBalances).toHaveLength(1);
    expect(result.current.metrics.bankBalances[0].label).toBe('Test Bank');
    expect(result.current.metrics.stablecoinBalances).toHaveLength(1);
    expect(result.current.metrics.paymentsToday).toBe(5);
    expect(result.current.metrics.feeSavingsYTD).toBe(15.5);
    expect(result.current.metrics.touchlessRate).toBe(85);
  });

  it('handles empty API responses by falling back to demo data', async () => {
    // Mock empty API responses
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ count: 0, payments: [] })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      });

    const { result } = renderHook(() => useCurrentMetrics(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should use demo data for empty responses
    expect(result.current.metrics.bankBalances).toHaveLength(1);
    expect(result.current.metrics.stablecoinBalances).toHaveLength(1);
    expect(result.current.metrics.paymentsToday).toBe(5); // Demo fallback
  });

  it('provides individual loading states', async () => {
    // Mock slow responses
    (fetch as jest.Mock)
      .mockImplementation(() => new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: async () => []
        }), 100)
      ));

    const { result } = renderHook(() => useCurrentMetrics(), {
      wrapper: createWrapper(),
    });

    // Initially should be loading
    expect(result.current.isLoading).toBe(true);
    expect(result.current.loadingStates.bankBalances).toBe(true);
    expect(result.current.loadingStates.stablecoinBalances).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.loadingStates.bankBalances).toBe(false);
    expect(result.current.loadingStates.stablecoinBalances).toBe(false);
  });

  it('handles API errors gracefully', async () => {
    // Mock API error responses
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: false,
        status: 500
      })
      .mockResolvedValueOnce({
        ok: false,
        status: 500
      })
      .mockResolvedValueOnce({
        ok: false,
        status: 500
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      });

    const { result } = renderHook(() => useCurrentMetrics(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Should still provide data (demo fallback)
    expect(result.current.metrics.bankBalances).toHaveLength(1);
    expect(result.current.metrics.stablecoinBalances).toHaveLength(1);
  });
});
