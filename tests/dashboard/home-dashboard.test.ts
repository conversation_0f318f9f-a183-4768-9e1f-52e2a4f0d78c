/**
 * Integration tests for the new CFO Dashboard
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock fetch for API calls
global.fetch = vi.fn();

describe('CFO Dashboard Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should load dashboard with KPI cards', async () => {
    // Mock successful API responses
    (fetch as any)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { accountId: 'JPM-001', label: 'JPMorgan', currency: 'USD', amount: 750000 }
        ]
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [
          { wallet: '0x456', symbol: 'USDC', amount: 245000 }
        ]
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ count: 8, payments: [] })
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      })
      .mockResolvedValueOnce({
        ok: true,
        json: async () => []
      });

    // Test that API endpoints are called
    expect(fetch).toHaveBeenCalledWith('/api/v1/liquidity/banks', expect.any(Object));
    expect(fetch).toHaveBeenCalledWith('/api/v1/liquidity/stablecoins', expect.any(Object));
    expect(fetch).toHaveBeenCalledWith('/api/v1/payments?from=today', expect.any(Object));
  });

  it('should handle API failures gracefully', async () => {
    // Mock failed API responses
    (fetch as any)
      .mockRejectedValueOnce(new Error('Network error'))
      .mockRejectedValueOnce(new Error('Network error'))
      .mockRejectedValueOnce(new Error('Network error'));

    // Should not throw errors and should fall back to demo data
    expect(() => {
      // Dashboard should handle errors gracefully
    }).not.toThrow();
  });

  it('should calculate metrics correctly', () => {
    // Test metric calculations
    const bankBalances = [
      { accountId: 'JPM-001', label: 'JPMorgan', currency: 'USD', amount: 500000 },
      { accountId: 'WF-002', label: 'Wells Fargo', currency: 'USD', amount: 300000 }
    ];

    const stablecoinBalances = [
      { wallet: '0x123', symbol: 'USDC', amount: 200000 }
    ];

    const totalLiquidity = bankBalances.reduce((sum, b) => sum + b.amount, 0) + 
                          stablecoinBalances.reduce((sum, s) => sum + s.amount, 0);

    expect(totalLiquidity).toBe(1000000);
  });

  it('should format currency values correctly', () => {
    const formatValue = (val: number) => {
      if (val >= 1000000) {
        return `$${(val / 1000000).toFixed(1)}M`;
      } else if (val >= 1000) {
        return `$${(val / 1000).toFixed(0)}K`;
      } else {
        return `$${val.toLocaleString()}`;
      }
    };

    expect(formatValue(1500000)).toBe('$1.5M');
    expect(formatValue(750000)).toBe('$750K');
    expect(formatValue(500)).toBe('$500');
  });

  it('should generate demo data with correct structure', () => {
    // Test demo data generation
    const demoSeries = Array.from({ length: 30 }, (_, i) => ({
      date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      value: Math.round(Math.random() * 1000)
    }));

    expect(demoSeries).toHaveLength(30);
    expect(demoSeries[0]).toHaveProperty('date');
    expect(demoSeries[0]).toHaveProperty('value');
    expect(typeof demoSeries[0].date).toBe('string');
    expect(typeof demoSeries[0].value).toBe('number');
  });

  it('should validate data freshness', () => {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
    
    // Data should be considered fresh if within 5 minutes
    const isDataFresh = (timestamp: Date) => {
      return (now.getTime() - timestamp.getTime()) < 5 * 60 * 1000;
    };

    expect(isDataFresh(now)).toBe(true);
    expect(isDataFresh(fiveMinutesAgo)).toBe(true);
    expect(isDataFresh(new Date(now.getTime() - 10 * 60 * 1000))).toBe(false);
  });
});
