/**
 * Unit tests for KPI Card components
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { KpiCard, LiquidityKpiCard, ExceptionKpiCard } from '../KpiCard';
import { DollarSign } from 'lucide-react';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

describe('KpiCard', () => {
  it('renders basic KPI card with value and title', () => {
    render(
      <KpiCard
        title="Test Metric"
        value={1000}
        subtitle="Test subtitle"
      />
    );

    expect(screen.getByText('Test Metric')).toBeInTheDocument();
    expect(screen.getByText('$1K')).toBeInTheDocument();
    expect(screen.getByText('Test subtitle')).toBeInTheDocument();
  });

  it('shows loading state correctly', () => {
    render(
      <KpiCard
        title="Test Metric"
        value={1000}
        isLoading={true}
      />
    );

    expect(screen.getByText('Test Metric')).toBeInTheDocument();
    // Loading skeleton should be present
    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(
      <KpiCard
        title="Test Metric"
        value={1000}
        onClick={handleClick}
      />
    );

    const card = screen.getByText('Test Metric').closest('.cursor-pointer');
    expect(card).toBeInTheDocument();
    
    if (card) {
      fireEvent.click(card);
      expect(handleClick).toHaveBeenCalledTimes(1);
    }
  });

  it('displays trend information correctly', () => {
    render(
      <KpiCard
        title="Test Metric"
        value={1000}
        trend={{
          value: 5.2,
          label: "vs last month",
          isPositive: true
        }}
      />
    );

    expect(screen.getByText('5.2% vs last month')).toBeInTheDocument();
    expect(screen.getByText('↗')).toBeInTheDocument();
  });

  it('displays negative trend correctly', () => {
    render(
      <KpiCard
        title="Test Metric"
        value={1000}
        trend={{
          value: 2.1,
          label: "vs last month",
          isPositive: false
        }}
      />
    );

    expect(screen.getByText('2.1% vs last month')).toBeInTheDocument();
    expect(screen.getByText('↘')).toBeInTheDocument();
  });

  it('formats large numbers correctly', () => {
    render(
      <KpiCard
        title="Large Number"
        value={1500000}
      />
    );

    expect(screen.getByText('$1.5M')).toBeInTheDocument();
  });

  it('displays icon when provided', () => {
    render(
      <KpiCard
        title="Test Metric"
        value={1000}
        icon={DollarSign}
      />
    );

    // Check if icon is rendered (lucide icons have specific attributes)
    expect(document.querySelector('svg')).toBeInTheDocument();
  });

  it('displays badge when provided', () => {
    render(
      <KpiCard
        title="Test Metric"
        value={1000}
        badge={{
          text: "Alert",
          variant: "destructive"
        }}
      />
    );

    expect(screen.getByText('Alert')).toBeInTheDocument();
  });
});

describe('LiquidityKpiCard', () => {
  const mockBankBalances = [
    { amount: 500000, currency: 'USD', label: 'Bank 1' },
    { amount: 300000, currency: 'USD', label: 'Bank 2' }
  ];

  const mockStablecoinBalances = [
    { amount: 200000, symbol: 'USDC' },
    { amount: 100000, symbol: 'USDT' }
  ];

  it('calculates total liquidity correctly', () => {
    render(
      <LiquidityKpiCard
        bankBalances={mockBankBalances}
        stablecoinBalances={mockStablecoinBalances}
      />
    );

    // Total should be 500k + 300k + 200k + 100k = 1.1M
    expect(screen.getByText('$1.1M')).toBeInTheDocument();
    expect(screen.getByText('Real-Time Liquidity')).toBeInTheDocument();
  });

  it('shows correct account counts', () => {
    render(
      <LiquidityKpiCard
        bankBalances={mockBankBalances}
        stablecoinBalances={mockStablecoinBalances}
      />
    );

    expect(screen.getByText('2 bank accounts + 2 stablecoin wallets')).toBeInTheDocument();
  });

  it('handles empty balances', () => {
    render(
      <LiquidityKpiCard
        bankBalances={[]}
        stablecoinBalances={[]}
      />
    );

    expect(screen.getByText('$0')).toBeInTheDocument();
    expect(screen.getByText('0 bank accounts + 0 stablecoin wallets')).toBeInTheDocument();
  });
});

describe('ExceptionKpiCard', () => {
  it('shows total exceptions correctly', () => {
    render(
      <ExceptionKpiCard
        pendingApprovals={5}
        openInvoices={3}
      />
    );

    expect(screen.getByText('8')).toBeInTheDocument();
    expect(screen.getByText('5 pending approvals, 3 open invoices')).toBeInTheDocument();
    expect(screen.getByText('Action Required')).toBeInTheDocument();
  });

  it('shows all clear when no exceptions', () => {
    render(
      <ExceptionKpiCard
        pendingApprovals={0}
        openInvoices={0}
      />
    );

    expect(screen.getByText('0')).toBeInTheDocument();
    expect(screen.getByText('All Clear')).toBeInTheDocument();
  });

  it('applies correct styling for exceptions', () => {
    const { container } = render(
      <ExceptionKpiCard
        pendingApprovals={5}
        openInvoices={3}
      />
    );

    // Should have red border/background for exceptions
    expect(container.querySelector('.border-red-200')).toBeInTheDocument();
  });
});
