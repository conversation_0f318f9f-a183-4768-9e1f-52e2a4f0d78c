/**
 * Demo Payment Gateway Tests
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Request, Response } from 'express';
import { demoPaymentGateway } from '../../server/services/demoPaymentGateway';

// Mock dependencies
vi.mock('../../server/storage', () => ({
  storage: {
    getPayment: vi.fn(),
    sendPayment: vi.fn(),
    markPaymentReceiptImported: vi.fn()
  }
}));

vi.mock('../../server/services/demoEventBus', () => ({
  demoEventBus: {
    emit: vi.fn()
  }
}));

vi.mock('../../server/middleware/demoMode', () => ({
  getDemoSpeed: vi.fn(() => 100) // Fast speed for testing
}));

describe('DemoPaymentGateway', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockJson: ReturnType<typeof vi.fn>;
  let mockStatus: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockJson = vi.fn();
    mockStatus = vi.fn(() => ({ json: mockJson }));
    
    mockReq = {
      params: { id: '1' },
      body: {}
    };
    
    mockRes = {
      json: mockJson,
      status: mockStatus
    };

    // Clear all pending payments before each test
    demoPaymentGateway.clearAllPending();
  });

  afterEach(() => {
    // Clean up any pending operations
    demoPaymentGateway.clearAllPending();
  });

  describe('handleSendPayment', () => {
    it('should handle payment send request successfully', async () => {
      const { storage } = await import('../../server/storage');
      
      // Mock payment data
      const mockPayment = {
        id: 1,
        approved: true,
        reference: 'PAY-001',
        amount: 1000,
        sender: 'Test Company'
      };

      vi.mocked(storage.getPayment).mockResolvedValue(mockPayment);

      await demoPaymentGateway.handleSendPayment(mockReq as Request, mockRes as Response);

      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'pending',
          txHash: expect.stringMatching(/^0x[a-f0-9]{64}$/),
          message: 'Payment is being processed on blockchain'
        })
      );
    });

    it('should return 404 for non-existent payment', async () => {
      const { storage } = await import('../../server/storage');
      
      vi.mocked(storage.getPayment).mockResolvedValue(undefined);

      await demoPaymentGateway.handleSendPayment(mockReq as Request, mockRes as Response);

      expect(mockStatus).toHaveBeenCalledWith(404);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Payment not found' });
    });

    it('should return 400 for unapproved payment', async () => {
      const { storage } = await import('../../server/storage');
      
      const mockPayment = {
        id: 1,
        approved: false,
        reference: 'PAY-001',
        amount: 1000,
        sender: 'Test Company'
      };

      vi.mocked(storage.getPayment).mockResolvedValue(mockPayment);

      await demoPaymentGateway.handleSendPayment(mockReq as Request, mockRes as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Payment must be approved before sending' });
    });
  });

  describe('handleIncomingPayment', () => {
    it('should handle incoming payment successfully', async () => {
      const { storage } = await import('../../server/storage');
      
      mockReq.body = {
        amount: 1000,
        reference: 'PAY-001',
        sender: 'External Company'
      };

      const mockReceivedPayment = {
        id: 1,
        amount: 1000,
        reference: 'PAY-001',
        sender: 'External Company'
      };

      vi.mocked(storage.markPaymentReceived).mockResolvedValue(mockReceivedPayment);

      await demoPaymentGateway.handleIncomingPayment(mockReq as Request, mockRes as Response);

      expect(mockJson).toHaveBeenCalledWith({
        status: 'success',
        receivedPayment: mockReceivedPayment
      });
    });

    it('should return 400 for missing required fields', async () => {
      mockReq.body = {
        amount: 1000
        // Missing reference and sender
      };

      await demoPaymentGateway.handleIncomingPayment(mockReq as Request, mockRes as Response);

      expect(mockStatus).toHaveBeenCalledWith(400);
      expect(mockJson).toHaveBeenCalledWith({ error: 'Missing required fields' });
    });
  });

  describe('pending payment management', () => {
    it('should track pending payments', async () => {
      const { storage } = await import('../../server/storage');
      
      const mockPayment = {
        id: 1,
        approved: true,
        reference: 'PAY-001',
        amount: 1000,
        sender: 'Test Company'
      };

      vi.mocked(storage.getPayment).mockResolvedValue(mockPayment);

      await demoPaymentGateway.handleSendPayment(mockReq as Request, mockRes as Response);

      const pendingPayments = demoPaymentGateway.getPendingPayments();
      expect(pendingPayments).toHaveLength(1);
      expect(pendingPayments[0]).toMatchObject({
        id: 1,
        status: 'PENDING',
        txHash: expect.stringMatching(/^0x[a-f0-9]{64}$/)
      });
    });

    it('should cancel pending payments', async () => {
      const { storage } = await import('../../server/storage');
      
      const mockPayment = {
        id: 1,
        approved: true,
        reference: 'PAY-001',
        amount: 1000,
        sender: 'Test Company'
      };

      vi.mocked(storage.getPayment).mockResolvedValue(mockPayment);

      await demoPaymentGateway.handleSendPayment(mockReq as Request, mockRes as Response);

      expect(demoPaymentGateway.getPendingPayments()).toHaveLength(1);

      const cancelled = demoPaymentGateway.cancelPayment(1);
      expect(cancelled).toBe(true);
      expect(demoPaymentGateway.getPendingPayments()).toHaveLength(0);
    });
  });
});
